const Store = require("../models/Store");
const upload = require("../config/multerConfig"); // Ensure multer is imported
const fs = require("fs");
const path = require("path");
const crypto = require("crypto");
const axios = require("axios");

// Add a new store (Admin only)
exports.addStore = async (req, res) => {
  try {
    const { name, systems } = req.body;

    const store = new Store({ name, systems });
    await store.save();

    res.status(201).json({ message: "Store added successfully", store });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// Update a store (Admin only)
exports.updateStore = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, systems } = req.body;

    const store = await Store.findByIdAndUpdate(id, { name, systems }, { new: true });

    if (!store) {
      return res.status(404).json({ message: "Store not found" });
    }

    res.status(200).json({ message: "Store updated successfully", store });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// Delete a store (Admin only)
exports.deleteStore = async (req, res) => {
  try {
    const { id } = req.params;

    const store = await Store.findByIdAndDelete(id);

    if (!store) {
      return res.status(404).json({ message: "Store not found" });
    }

    res.status(200).json({ message: "Store deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// Get all stores (Admin and Store users)
exports.getStores = async (req, res) => {
  try {
    let stores;
    if (req.user.role === "admin") {
      stores = await Store.find();
    } else {
      stores = await Store.find({ _id: req.user.storeId });
    }
    res.status(200).json(stores);
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// Add a new method to handle flyer upload
exports.uploadFlyer = async (req, res) => {
  try {
    const { id } = req.params;
    const storeName = req.headers['x-store-name'];

    console.log('Upload flyer request:', {
      storeId: id,
      storeName,
      userStoreId: req.user.storeId,
      userRole: req.user.role
    });

    if (!storeName) {
      return res.status(400).json({ message: "Store name is required" });
    }

    // Find the store by ID
    const store = await Store.findById(id);
    console.log('Found store:', store); // Debug log

    if (!store) {
      return res.status(404).json({ message: "Store not found" });
    }

    // Verify user has permission for this store
    if (req.user.role === 'store' && req.user.storeId.toString() !== id) {
      return res.status(403).json({ message: "Unauthorized: You can only upload flyers to your own store" });
    }

    // If a new flyer is uploaded, delete the old flyer and update the path
    if (req.file) {
      // Delete the old flyer file if it exists
      if (store.flyer && fs.existsSync(store.flyer)) {
        fs.unlinkSync(store.flyer);
      }

      // Update the flyer path
      store.flyer = req.file.path.replace(/\\/g, '/');
    }

    await store.save();

    res.status(200).json({ 
      message: "Flyer uploaded successfully", 
      flyer: store.flyer 
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// Get flyer for a specific store
exports.getFlyer = async (req, res) => {
  try {
    const { id } = req.params;

    const store = await Store.findById(id);
    if (!store) {
      return res.status(404).json({ message: "Store not found" });
    }

    if (!store.flyer) {
      return res.status(404).json({ message: "No flyer found for this store" });
    }

    // Check if the file exists
    if (!fs.existsSync(store.flyer)) {
      return res.status(404).json({ message: "Flyer file not found" });
    }

    // Return the flyer path
    const flyerUrl = `/uploads/${path.basename(store.flyer)}`;
    res.status(200).json({ 
      message: "Flyer found",
      flyer: flyerUrl,
      storeName: store.name 
    });

  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// Configure PayFast settings for a store
exports.configurePayFast = async (req, res) => {
  try {
    const { id } = req.params;
    const { merchantId, merchantKey, passphrase, sandbox = true, posDeviceId } = req.body;

    // Validate required fields
    if (!merchantId || !merchantKey || !passphrase) {
      return res.status(400).json({
        message: "Missing required PayFast credentials: merchantId, merchantKey, passphrase"
      });
    }

    // For store users, verify they can only update their own store
    if (req.user.role === "store" && req.user.storeId.toString() !== id) {
      return res.status(403).json({ message: "Access denied. Can only configure your own store." });
    }

    const store = await Store.findById(id);
    if (!store) {
      return res.status(404).json({ message: "Store not found" });
    }

    // Update PayFast configuration
    store.payfast = {
      merchantId,
      merchantKey,
      passphrase,
      sandbox,
      posDeviceId,
      isActive: true,
      setupComplete: true
    };

    await store.save();

    res.status(200).json({
      message: "PayFast configuration updated successfully",
      payfast: {
        merchantId: store.payfast.merchantId,
        sandbox: store.payfast.sandbox,
        posDeviceId: store.payfast.posDeviceId,
        isActive: store.payfast.isActive,
        setupComplete: store.payfast.setupComplete
      }
    });

  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// Get PayFast configuration for a store
exports.getPayFastConfig = async (req, res) => {
  try {
    const { id } = req.params;

    // For store users, verify they can only access their own store
    if (req.user.role === "store" && req.user.storeId.toString() !== id) {
      return res.status(403).json({ message: "Access denied" });
    }

    const store = await Store.findById(id);
    if (!store) {
      return res.status(404).json({ message: "Store not found" });
    }

    // Return PayFast config without sensitive data
    res.status(200).json({
      payfast: {
        merchantId: store.payfast?.merchantId || '',
        sandbox: store.payfast?.sandbox || true,
        posDeviceId: store.payfast?.posDeviceId || '',
        isActive: store.payfast?.isActive || false,
        setupComplete: store.payfast?.setupComplete || false
      }
    });

  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// Test PayFast connection
exports.testPayFastConnection = async (req, res) => {
  try {
    const { id } = req.params;

    // For store users, verify they can only test their own store
    if (req.user.role === "store" && req.user.storeId.toString() !== id) {
      return res.status(403).json({ message: "Access denied" });
    }

    const store = await Store.findById(id);
    if (!store) {
      return res.status(404).json({ message: "Store not found" });
    }

    if (!store.payfast || !store.payfast.setupComplete) {
      return res.status(400).json({
        message: "PayFast not configured for this store"
      });
    }

    // Test PayFast connection by validating credentials
    const testData = {
      merchant_id: store.payfast.merchantId,
      merchant_key: store.payfast.merchantKey,
      amount: '1.00',
      item_name: 'Test transaction'
    };

    // Generate signature for test
    const paramString = Object.keys(testData)
      .sort()
      .map(key => `${key}=${encodeURIComponent(testData[key])}`)
      .join('&');

    const stringToHash = `${paramString}&passphrase=${encodeURIComponent(store.payfast.passphrase)}`;
    const signature = crypto.createHash('md5').update(stringToHash).digest('hex');

    // For now, just validate that we can generate a signature
    // In production, you might make a test API call to PayFast

    res.status(200).json({
      success: true,
      message: "PayFast connection test successful",
      sandbox: store.payfast.sandbox,
      merchantId: store.payfast.merchantId
    });

  } catch (error) {
    res.status(500).json({
      message: "PayFast connection test failed",
      error: error.message
    });
  }
};

// Activate/Deactivate PayFast for a store
exports.togglePayFastStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;

    // For store users, verify they can only update their own store
    if (req.user.role === "store" && req.user.storeId.toString() !== id) {
      return res.status(403).json({ message: "Access denied" });
    }

    const store = await Store.findById(id);
    if (!store) {
      return res.status(404).json({ message: "Store not found" });
    }

    if (!store.payfast || !store.payfast.setupComplete) {
      return res.status(400).json({
        message: "PayFast must be configured before activation"
      });
    }

    store.payfast.isActive = isActive;
    await store.save();

    res.status(200).json({
      message: `PayFast ${isActive ? 'activated' : 'deactivated'} successfully`,
      isActive: store.payfast.isActive
    });

  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};