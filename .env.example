# Self-Checkout System Environment Configuration
# Copy this file to .env and update with your values

# ==============================================
# DATABASE CONFIGURATION
# ==============================================

# MongoDB connection string
# Local: mongodb://localhost:27017/selfcheckout
# Atlas: mongodb+srv://username:<EMAIL>/selfcheckout
MONGODB_URI=mongodb://localhost:27017/selfcheckout

# ==============================================
# SECURITY CONFIGURATION
# ==============================================

# JWT secret for token signing (minimum 32 characters)
# Generate with: openssl rand -base64 32
JWT_SECRET=your_super_secure_jwt_secret_here_minimum_32_characters

# ==============================================
# SERVER CONFIGURATION
# ==============================================

# Server port
PORT=5000

# Environment (development, production, test)
NODE_ENV=development

# Base URL for the application
BASE_URL=http://localhost:5000

# ==============================================
# FILE UPLOAD CONFIGURATION
# ==============================================

# Maximum file size for uploads (in bytes)
# 5MB = 5242880 bytes
MAX_FILE_SIZE=5242880

# Upload directory path
UPLOAD_PATH=./uploads

# ==============================================
# PAYFAST CONFIGURATION
# ==============================================

# PayFast sandbox mode (true for testing, false for production)
PAYFAST_SANDBOX=true

# ==============================================
# DOCKER CONFIGURATION
# ==============================================

# MongoDB password for Docker deployment
MONGO_PASSWORD=password123

# API URL for frontend (Docker)
API_URL=http://localhost:5000/api

# ==============================================
# OPTIONAL CONFIGURATIONS
# ==============================================

# Redis URL for session management (optional)
# REDIS_URL=redis://localhost:6379

# Email configuration for notifications (optional)
# EMAIL_HOST=smtp.gmail.com
# EMAIL_PORT=587
# EMAIL_USER=<EMAIL>
# EMAIL_PASS=your-app-password

# Logging level (error, warn, info, debug)
# LOG_LEVEL=info

# Rate limiting (requests per minute)
# RATE_LIMIT=100

# CORS origins (comma-separated)
# CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# ==============================================
# STORE-SPECIFIC PAYFAST CONFIGURATION
# ==============================================
# Note: These are configured per store in the admin interface
# This is just for reference

# Example PayFast credentials (sandbox)
# PAYFAST_MERCHANT_ID=10000100
# PAYFAST_MERCHANT_KEY=46f0cd694581a
# PAYFAST_PASSPHRASE=jt7NOE43FZPn

# ==============================================
# DEVELOPMENT CONFIGURATION
# ==============================================

# Enable debug logging
# DEBUG=true

# Enable API documentation
# ENABLE_DOCS=true

# Enable request logging
# ENABLE_REQUEST_LOGGING=true
