# PayFast POS Integration Architecture

## Overview
This document outlines the architecture for integrating PayFast POS card machines with our self-checkout system, ensuring payments go directly to individual store accounts rather than a central account.

## Key Requirements
- Each store has their own PayFast merchant account
- Payments route directly to store accounts
- Support for PayFast POS card machines (R999-R1499 devices)
- Real-time payment processing and confirmation
- Webhook integration for payment status updates

## Architecture Components

### 1. Store Model Enhancement
```javascript
// Enhanced Store Schema
{
  name: String,
  email: String,
  password: String,
  systems: [SystemSchema],
  flyer: String,
  payfast: {
    merchantId: String,        // Store-specific PayFast merchant ID
    merchantKey: String,       // Store-specific merchant key
    passphrase: String,        // Store-specific passphrase
    sandbox: Boolean,          // Environment flag
    posDeviceId: String,       // POS device identifier
    isActive: Boolean,         // Payment processing status
    setupComplete: Boolean     // Configuration completion flag
  },
  createdAt: Date
}
```

### 2. Transaction Model Enhancement
```javascript
// Enhanced Transaction Schema
{
  storeId: ObjectId,           // Link to specific store
  systemId: String,            // Checkout system identifier
  amount: Number,
  currency: String,
  status: String,              // pending, completed, failed, refunded
  transactionId: String,       // Our internal transaction ID
  payfast: {
    paymentId: String,         // PayFast payment ID
    merchantTransactionId: String, // PayFast merchant transaction ID
    signature: String,         // PayFast signature for verification
    paymentMethod: String,     // card, contactless, etc.
    cardType: String,          // visa, mastercard, etc.
    maskedCardNumber: String   // Last 4 digits for reference
  },
  items: [{
    productId: ObjectId,
    name: String,
    price: Number,
    quantity: Number,
    barcode: String
  }],
  receipt: {
    generated: Boolean,
    path: String,
    emailSent: Boolean,
    printed: Boolean
  },
  createdAt: Date,
  completedAt: Date
}
```

### 3. Payment Processing Flow

#### Step 1: Payment Initiation
```javascript
// Desktop app initiates payment
POST /api/payments/initiate
{
  storeId: "store_id",
  systemId: "system_id", 
  amount: 150.00,
  items: [...],
  paymentMethod: "card"
}
```

#### Step 2: PayFast POS Integration
```javascript
// Backend processes payment with store-specific credentials
const payfastConfig = {
  merchantId: store.payfast.merchantId,
  merchantKey: store.payfast.merchantKey,
  passphrase: store.payfast.passphrase,
  sandbox: store.payfast.sandbox
};

// Initiate payment with PayFast POS API
const paymentRequest = {
  merchant_id: payfastConfig.merchantId,
  merchant_key: payfastConfig.merchantKey,
  amount: amount * 100, // Convert to cents
  item_name: "Self-checkout purchase",
  return_url: `${baseUrl}/api/payments/return`,
  cancel_url: `${baseUrl}/api/payments/cancel`,
  notify_url: `${baseUrl}/api/payments/webhook`,
  custom_str1: transactionId,
  custom_str2: storeId
};
```

#### Step 3: Real-time Status Updates
```javascript
// Webhook endpoint for PayFast notifications
POST /api/payments/webhook
{
  m_payment_id: "payfast_payment_id",
  pf_payment_id: "payfast_internal_id",
  payment_status: "COMPLETE",
  item_name: "Self-checkout purchase",
  amount_gross: "150.00",
  custom_str1: "our_transaction_id",
  custom_str2: "store_id",
  signature: "generated_signature"
}
```

### 4. API Endpoints Structure

#### Payment Endpoints
- `POST /api/payments/initiate` - Start payment process
- `POST /api/payments/webhook` - PayFast webhook handler
- `GET /api/payments/status/:transactionId` - Check payment status
- `POST /api/payments/refund` - Process refunds
- `GET /api/payments/history/:storeId` - Payment history

#### Store Configuration Endpoints
- `PUT /api/stores/:id/payfast` - Configure PayFast settings
- `GET /api/stores/:id/payfast/test` - Test PayFast connection
- `POST /api/stores/:id/payfast/activate` - Activate payment processing

### 5. Security Considerations

#### Signature Verification
```javascript
// Verify PayFast webhook signatures
function verifyPayFastSignature(data, signature, passphrase) {
  const paramString = Object.keys(data)
    .sort()
    .map(key => `${key}=${encodeURIComponent(data[key])}`)
    .join('&');
  
  const stringToHash = paramString + '&passphrase=' + passphrase;
  const calculatedSignature = crypto
    .createHash('md5')
    .update(stringToHash)
    .digest('hex');
    
  return calculatedSignature === signature;
}
```

#### Environment Configuration
```javascript
// Environment-specific settings
const payfastConfig = {
  sandbox: {
    baseUrl: 'https://sandbox.payfast.co.za',
    merchantId: 'sandbox_merchant_id',
    merchantKey: 'sandbox_merchant_key'
  },
  production: {
    baseUrl: 'https://www.payfast.co.za',
    merchantId: process.env.PAYFAST_MERCHANT_ID,
    merchantKey: process.env.PAYFAST_MERCHANT_KEY
  }
};
```

### 6. Error Handling & Retry Logic

#### Payment Failure Scenarios
- Network connectivity issues
- PayFast service unavailability  
- Invalid merchant credentials
- Insufficient funds
- Card declined

#### Retry Strategy
```javascript
const retryConfig = {
  maxRetries: 3,
  retryDelay: 2000, // 2 seconds
  backoffMultiplier: 2
};

async function processPaymentWithRetry(paymentData, retries = 0) {
  try {
    return await processPayment(paymentData);
  } catch (error) {
    if (retries < retryConfig.maxRetries && isRetryableError(error)) {
      const delay = retryConfig.retryDelay * Math.pow(retryConfig.backoffMultiplier, retries);
      await new Promise(resolve => setTimeout(resolve, delay));
      return processPaymentWithRetry(paymentData, retries + 1);
    }
    throw error;
  }
}
```

### 7. Reporting & Analytics

#### Store-Specific Revenue Tracking
```javascript
// Aggregate transactions by store
const storeRevenue = await Transaction.aggregate([
  { $match: { status: 'completed', storeId: ObjectId(storeId) } },
  { $group: {
    _id: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
    totalRevenue: { $sum: "$amount" },
    transactionCount: { $sum: 1 },
    averageTransaction: { $avg: "$amount" }
  }},
  { $sort: { _id: 1 } }
]);
```

### 8. Implementation Phases

#### Phase 1: Core Integration
- Store PayFast configuration
- Basic payment processing
- Webhook handling
- Transaction recording

#### Phase 2: Enhanced Features  
- Refund processing
- Payment method tracking
- Error handling & retries
- Real-time status updates

#### Phase 3: Advanced Features
- Split payments
- Recurring payments
- Advanced reporting
- Multi-currency support

## Benefits of This Architecture

1. **Direct Store Payments**: Money goes directly to store accounts
2. **Scalability**: Each store operates independently
3. **Security**: Store-specific credentials and signature verification
4. **Compliance**: Follows PayFast security standards
5. **Flexibility**: Supports multiple payment methods and devices
6. **Transparency**: Real-time transaction tracking and reporting

## Next Steps

1. Implement enhanced Store and Transaction models
2. Create PayFast payment controller
3. Set up webhook endpoints
4. Add store configuration interface
5. Implement error handling and retry logic
6. Create comprehensive testing suite
7. Deploy and test with PayFast sandbox
8. Go live with production credentials
