const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const Store = require('../models/Store');
const { authenticate, authorize } = require('../middleware/authMiddleware');

// Authenticate a system
router.post('/auth', async (req, res) => {
    try {
        const { systemId, storeName } = req.body;

        if (!systemId) {
            return res.status(400).json({
                message: 'System ID is required'
            });
        }

        // Find store that owns this system
        const store = await Store.findOne({
            'systems.systemId': systemId
        });

        if (!store) {
            return res.status(401).json({
                message: 'Invalid system ID or system not registered'
            });
        }

        // Verify system is active
        const system = store.systems.find(s => s.systemId === systemId);
        if (system.status !== 'Active') {
            return res.status(401).json({
                message: 'System is not active'
            });
        }

        // Optional: Verify store name matches for additional security
        if (storeName && store.name !== storeName) {
            return res.status(401).json({
                message: 'Store name mismatch'
            });
        }

        // Generate JWT token for system
        const token = jwt.sign(
            {
                systemId,
                storeId: store._id,
                storeName: store.name,
                type: 'system',
                systemLocation: system.location
            },
            process.env.JWT_SECRET || 'fallback_secret',
            { expiresIn: '24h' }
        );

        // Update system last seen
        system.lastSeen = new Date();
        await store.save();

        res.json({
            success: true,
            token,
            storeId: store._id,
            storeName: store.name,
            systemLocation: system.location,
            expiresIn: '24h'
        });

    } catch (error) {
        console.error('System authentication error:', error);
        res.status(500).json({
            message: 'Authentication failed',
            error: error.message
        });
    }
});

// Register a new system to a store
router.post('/register', authenticate, authorize(['admin', 'store']), async (req, res) => {
    try {
        const { storeId, location, systemName } = req.body;

        if (!storeId || !location) {
            return res.status(400).json({
                message: 'Store ID and location are required'
            });
        }

        // Verify store access
        if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
            return res.status(403).json({ message: "Access denied" });
        }

        const store = await Store.findById(storeId);
        if (!store) {
            return res.status(404).json({ message: 'Store not found' });
        }

        // Generate unique system ID
        const systemId = `${store.name.substring(0, 3).toUpperCase()}-${crypto.randomBytes(4).toString('hex')}-${Date.now().toString(36).toUpperCase()}`;

        // Check if location already exists for this store
        const existingSystem = store.systems.find(s => s.location === location);
        if (existingSystem) {
            return res.status(400).json({
                message: 'A system already exists at this location'
            });
        }

        // Add new system
        const newSystem = {
            systemId,
            location,
            status: 'Active',
            name: systemName || `Checkout ${location}`,
            registeredAt: new Date(),
            lastSeen: null
        };

        store.systems.push(newSystem);
        await store.save();

        res.status(201).json({
            success: true,
            message: 'System registered successfully',
            system: {
                systemId,
                location,
                name: newSystem.name,
                status: newSystem.status
            }
        });

    } catch (error) {
        console.error('System registration error:', error);
        res.status(500).json({
            message: 'System registration failed',
            error: error.message
        });
    }
});

// Get systems for a store
router.get('/store/:storeId', authenticate, authorize(['admin', 'store']), async (req, res) => {
    try {
        const { storeId } = req.params;

        // Verify store access
        if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
            return res.status(403).json({ message: "Access denied" });
        }

        const store = await Store.findById(storeId);
        if (!store) {
            return res.status(404).json({ message: 'Store not found' });
        }

        res.json({
            systems: store.systems.map(system => ({
                systemId: system.systemId,
                location: system.location,
                name: system.name,
                status: system.status,
                registeredAt: system.registeredAt,
                lastSeen: system.lastSeen
            }))
        });

    } catch (error) {
        console.error('Get systems error:', error);
        res.status(500).json({
            message: 'Failed to get systems',
            error: error.message
        });
    }
});

// Update system status
router.put('/:systemId/status', authenticate, authorize(['admin', 'store']), async (req, res) => {
    try {
        const { systemId } = req.params;
        const { status } = req.body;

        if (!['Active', 'Inactive', 'Maintenance'].includes(status)) {
            return res.status(400).json({
                message: 'Invalid status. Must be Active, Inactive, or Maintenance'
            });
        }

        const store = await Store.findOne({ 'systems.systemId': systemId });
        if (!store) {
            return res.status(404).json({ message: 'System not found' });
        }

        // Verify store access
        if (req.user.role === "store" && req.user.storeId.toString() !== store._id.toString()) {
            return res.status(403).json({ message: "Access denied" });
        }

        const system = store.systems.find(s => s.systemId === systemId);
        system.status = status;
        await store.save();

        res.json({
            success: true,
            message: 'System status updated',
            system: {
                systemId: system.systemId,
                location: system.location,
                status: system.status
            }
        });

    } catch (error) {
        console.error('Update system status error:', error);
        res.status(500).json({
            message: 'Failed to update system status',
            error: error.message
        });
    }
});

module.exports = router; 