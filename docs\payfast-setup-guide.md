# PayFast POS Integration Setup Guide

## Overview
This guide walks you through setting up PayFast POS integration for your self-checkout system, ensuring payments go directly to individual store accounts.

## Prerequisites

### 1. PayFast Account Setup
Each store needs their own PayFast merchant account:

1. **Sign up for PayFast**: Visit [payfast.io](https://payfast.io)
2. **Choose POS Solution**: Select "Point-of-Sale Device" option
3. **Complete Verification**: Provide business documents and banking details
4. **Order POS Device**: Choose between:
   - Digital Card Machine: R999 (once-off)
   - Printer Card Machine: R1,499 (once-off)
5. **Monthly Fee**: R49/month connectivity fee

### 2. Get PayFast Credentials
For each store, obtain:
- **Merchant ID**: Found in PayFast dashboard
- **Merchant Key**: Found in PayFast dashboard  
- **Passphrase**: Set in PayFast settings
- **POS Device ID**: Provided with physical device

### 3. Environment Setup
```bash
# Install dependencies
cd backend
npm install

# Set environment variables
echo "JWT_SECRET=your_jwt_secret_here" >> .env
echo "MONGODB_URI=your_mongodb_connection_string" >> .env
echo "BASE_URL=http://localhost:5000" >> .env
```

## Configuration Steps

### Step 1: Start the Backend Server
```bash
cd backend
npm start
```

### Step 2: Configure Store PayFast Settings

#### Via API (Recommended)
```bash
# Login as admin
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your_password"
  }'

# Configure PayFast for store
curl -X PUT http://localhost:5000/api/stores/{STORE_ID}/payfast \
  -H "Authorization: Bearer {ADMIN_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "merchantId": "your_merchant_id",
    "merchantKey": "your_merchant_key", 
    "passphrase": "your_passphrase",
    "sandbox": false,
    "posDeviceId": "your_pos_device_id"
  }'
```

#### Via Admin Dashboard
1. Login to admin dashboard
2. Navigate to Stores → Select Store → PayFast Settings
3. Enter PayFast credentials
4. Test connection
5. Activate payment processing

### Step 3: Register Checkout Systems
```bash
# Register a new checkout system
curl -X POST http://localhost:5000/api/systems/register \
  -H "Authorization: Bearer {ADMIN_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "storeId": "{STORE_ID}",
    "location": "Checkout 1",
    "systemName": "Main Checkout"
  }'
```

### Step 4: Configure Desktop Application
Update `system_config.json`:
```json
{
    "system_id": "GENERATED_SYSTEM_ID",
    "store_name": "Your Store Name",
    "store_id": "STORE_OBJECT_ID"
}
```

## Testing the Integration

### 1. Run Integration Tests
```bash
# Update test configuration
node test-payfast-integration.js
```

### 2. Manual Testing Steps

#### Test System Authentication
1. Start desktop application
2. Verify system authenticates successfully
3. Check authentication token is received

#### Test Payment Flow
1. Scan test products
2. Initiate card payment
3. Verify PayFast payment data is generated
4. Check transaction is recorded in database

#### Test Webhook Handling
1. Simulate PayFast webhook
2. Verify transaction status updates
3. Check payment completion flow

### 3. Sandbox Testing
Use PayFast sandbox credentials for testing:
```json
{
  "merchantId": "10000100",
  "merchantKey": "46f0cd694581a",
  "passphrase": "jt7NOE43FZPn",
  "sandbox": true
}
```

## Production Deployment

### 1. Security Checklist
- [ ] Use HTTPS for all API endpoints
- [ ] Secure PayFast credentials in environment variables
- [ ] Enable webhook signature verification
- [ ] Implement rate limiting on payment endpoints
- [ ] Set up monitoring and alerting

### 2. PayFast Production Setup
1. **Switch to Production**: Change `sandbox: false` in store config
2. **Update Credentials**: Use production PayFast credentials
3. **Configure Webhooks**: Set webhook URL to your production domain
4. **Test Thoroughly**: Perform end-to-end testing

### 3. Monitoring Setup
```javascript
// Add to your monitoring system
const paymentMetrics = {
  successfulPayments: 0,
  failedPayments: 0,
  averageTransactionValue: 0,
  webhookFailures: 0
};
```

## Troubleshooting

### Common Issues

#### 1. Authentication Failures
**Problem**: System can't authenticate
**Solution**: 
- Verify system ID is registered in store
- Check system status is 'Active'
- Ensure store name matches configuration

#### 2. Payment Initiation Fails
**Problem**: Payment requests fail
**Solution**:
- Verify PayFast credentials are correct
- Check store PayFast configuration is complete
- Ensure PayFast account is active

#### 3. Webhook Not Received
**Problem**: Payment status doesn't update
**Solution**:
- Check webhook URL is accessible
- Verify signature validation
- Check PayFast webhook configuration

#### 4. Signature Verification Fails
**Problem**: Webhook signature invalid
**Solution**:
- Verify passphrase matches PayFast settings
- Check signature generation algorithm
- Ensure all webhook data is included

### Debug Mode
Enable debug logging:
```javascript
// In backend
process.env.DEBUG_PAYFAST = 'true';

// In desktop app
console.log('Payment data:', paymentData);
console.log('Response:', response.data);
```

## Support and Resources

### PayFast Resources
- [PayFast Developer Documentation](https://developers.payfast.co.za/)
- [PayFast Support](https://support.payfast.help/)
- [PayFast Status Page](https://status.payfast.io/)

### Integration Support
- Check logs in `backend/error.log`
- Review transaction records in database
- Monitor webhook delivery in PayFast dashboard
- Contact PayFast support for payment gateway issues

### Performance Optimization
- Implement connection pooling for database
- Cache store configurations
- Use async processing for webhooks
- Monitor payment processing times

## Security Best Practices

1. **Credential Management**
   - Store PayFast credentials securely
   - Rotate credentials regularly
   - Use environment variables in production

2. **Webhook Security**
   - Always verify webhook signatures
   - Implement idempotency for webhook processing
   - Log all webhook attempts

3. **Transaction Security**
   - Validate all payment amounts
   - Implement transaction limits
   - Monitor for suspicious activity

4. **System Security**
   - Secure system registration process
   - Implement system heartbeat monitoring
   - Regular security audits

## Compliance

### PCI DSS Compliance
- PayFast handles PCI DSS compliance for card data
- Ensure your system doesn't store card information
- Follow PayFast security guidelines

### Data Protection
- Encrypt sensitive data in transit and at rest
- Implement proper access controls
- Regular security assessments
- Comply with local data protection laws

## Scaling Considerations

### Multi-Store Deployment
- Each store has independent PayFast configuration
- Centralized monitoring and reporting
- Store-specific payment routing
- Independent settlement accounts

### High Availability
- Implement payment retry logic
- Set up redundant webhook endpoints
- Monitor payment processing health
- Automated failover procedures
