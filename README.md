# 🛒 Self-Checkout System

A complete, production-ready self-checkout system with PayFast POS integration, designed for multi-store retail operations with Raspberry Pi-based trolley deployment.

## 🌟 Features

### 💳 **PayFast POS Integration**
- Direct payments to individual store accounts
- Support for card and contactless payments
- Real-time payment processing and confirmation
- Webhook integration for payment status updates

### 🏪 **Multi-Store Architecture**
- Independent store management
- Store-specific product catalogs
- Individual PayFast merchant accounts
- Centralized admin oversight

### 🛒 **Self-Checkout Experience**
- Touchscreen-friendly interface
- Camera-based barcode scanning
- Real-time product lookup
- Receipt generation and printing
- Multiple payment methods

### 📊 **Comprehensive Reporting**
- Store-specific analytics
- Cross-store performance metrics
- Transaction tracking
- Revenue reporting

### 🔧 **Hardware Integration**
- Raspberry Pi deployment
- Touchscreen support
- USB camera integration
- Receipt printer support
- PayFast POS terminal connectivity

## 🏗️ System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │   Backend API   │    │    Database     │
│   (React/Vite)  │◄──►│ (Node.js/Express)│◄──►│   (MongoDB)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  PayFast POS    │
                       │   Integration   │
                       └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │ Raspberry Pi    │
                       │ Self-Checkout   │
                       │ (Python/Tkinter)│
                       └─────────────────┘
```

## 🚀 Quick Start

### Option 1: Automated Installation
```bash
# Clone the repository
git clone <your-repo-url>
cd checkout

# Run the master installation script
chmod +x install.sh
./install.sh
```

### Option 2: Manual Installation
```bash
# Backend setup
cd backend
chmod +x ../scripts/install-backend.sh
../scripts/install-backend.sh

# Frontend setup
cd ../ui
chmod +x ../scripts/install-frontend.sh
../scripts/install-frontend.sh
```

### Option 3: Docker Deployment
```bash
# Start with Docker Compose
docker-compose up -d

# Or with Docker Compose V2
docker compose up -d
```

## 📦 Components

### 🔧 **Backend API** (`/backend/`)
- **Technology**: Node.js, Express, MongoDB, Mongoose
- **Features**: REST API, Authentication, PayFast integration, File uploads
- **Port**: 5000
- **Documentation**: http://localhost:5000/api-docs

### 🌐 **Web Frontend** (`/ui/`)
- **Technology**: React 18, Vite, Material-UI
- **Features**: Admin dashboard, Store management, Product management
- **Port**: 5173 (dev), 3000 (production)
- **Access**: http://localhost:5173

### 🍓 **Desktop App** (`sys2.py`)
- **Technology**: Python, Tkinter, OpenCV, ttkbootstrap
- **Features**: Barcode scanning, Payment processing, Receipt printing
- **Deployment**: Raspberry Pi with touchscreen

## 🔧 Configuration

### Environment Variables
Copy `.env.example` to `.env` and configure:

```env
# Database
MONGODB_URI=mongodb://localhost:27017/selfcheckout

# Security
JWT_SECRET=your_super_secure_jwt_secret_here

# Server
PORT=5000
BASE_URL=http://localhost:5000

# PayFast
PAYFAST_SANDBOX=true
```

### PayFast Setup
1. **Create PayFast Account** for each store
2. **Order POS Device** (R999-R1499 + R49/month)
3. **Configure in Admin Panel**:
   - Merchant ID
   - Merchant Key
   - Passphrase
   - POS Device ID

### Raspberry Pi Configuration
```json
{
    "system_id": "STORE1-TROLLEY-001",
    "store_name": "Your Store Name",
    "store_id": "mongodb_store_id",
    "backend_url": "http://your-server:5000/api"
}
```

## 🛠️ Hardware Requirements

### **Server/Development Machine**
- CPU: 2+ cores, 4GB+ RAM
- Storage: 20GB+ available
- OS: Windows/macOS/Linux
- Network: Stable internet

### **Raspberry Pi Trolley Setup**
- Raspberry Pi 4 (4GB+ RAM)
- 7" Touchscreen display
- USB Camera (1080p)
- PayFast POS terminal
- Thermal receipt printer
- 32GB+ microSD card
- Power supply (5V 3A)

## 📚 Documentation

- **[Complete Installation Guide](INSTALLATION_GUIDE.md)** - Detailed setup instructions
- **[PayFast Setup Guide](docs/payfast-setup-guide.md)** - Payment integration
- **[Architecture Documentation](docs/payfast-integration-architecture.md)** - System design
- **[API Documentation](http://localhost:5000/api-docs)** - REST API reference

## 🔐 Default Credentials

### Admin Login
- **Email**: `<EMAIL>`
- **Password**: `admin123`

### PayFast Sandbox
- **Merchant ID**: `10000100`
- **Merchant Key**: `46f0cd694581a`
- **Passphrase**: `jt7NOE43FZPn`

## 🚀 Deployment

### Development
```bash
# Start all services
./start.sh

# Check status
./status.sh

# Stop all services
./stop.sh
```

### Production
```bash
# Backend with PM2
cd backend
pm2 start server.js --name selfcheckout-api

# Frontend build
cd ui
npm run build
serve -s dist

# Or use Docker
docker-compose up -d
```

### Raspberry Pi
```bash
# Install on Pi
chmod +x scripts/install-pi.sh
./scripts/install-pi.sh

# Copy application
scp sys2.py system_config.json pi@<pi-ip>:/home/<USER>/selfcheckout/

# Enable auto-start
sudo systemctl start selfcheckout.service
```

## 🧪 Testing

### Integration Tests
```bash
# Test PayFast integration
node test-payfast-integration.js

# Test API endpoints
npm test
```

### Manual Testing
1. **Web Interface**: Login and configure stores
2. **Desktop App**: Test barcode scanning and payments
3. **PayFast**: Process test transactions
4. **Reports**: Generate analytics

## 🔧 Troubleshooting

### Common Issues

#### Backend Won't Start
```bash
# Check MongoDB
mongo --eval "db.adminCommand('ismaster')"

# Check logs
npm start 2>&1 | tee backend.log
```

#### Camera Not Working (Pi)
```bash
# Enable camera
sudo raspi-config

# Test camera
raspistill -o test.jpg
```

#### PayFast Integration Issues
```bash
# Test credentials
curl -X GET http://localhost:5000/api/stores/STORE_ID/payfast/test \
  -H "Authorization: Bearer TOKEN"
```

## 📞 Support

### Resources
- [PayFast Developer Docs](https://developers.payfast.co.za/)
- [MongoDB Documentation](https://docs.mongodb.com/)
- [React Documentation](https://react.dev/)

### Logs
- Backend: `backend/error.log`
- Frontend: Browser console
- Desktop: Terminal output
- System: `journalctl -u selfcheckout.service`

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- PayFast for payment processing
- MongoDB for database solutions
- React team for the frontend framework
- OpenCV for computer vision capabilities

---

**🎉 Ready to revolutionize your retail experience with self-checkout technology!**

For detailed installation instructions, see [INSTALLATION_GUIDE.md](INSTALLATION_GUIDE.md)
