import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Alert,
  IconButton,
  Menu,
  MenuItem,
  Tooltip,
  Badge,
  LinearProgress,
  Avatar,
  Divider,
  Tab,
  Tabs,
  FormControl,
  InputLabel,
  Select,
  Switch,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  Analytics,
  TrendingUp,
  TrendingDown,
  Assessment,
  Timeline,
  BarChart,
  PieChart,
  ShowChart,
  Insights,
  Forecast,
  CompareArrows,
  Report,
  Download,
  Refresh,
  DateRange,
  FilterList,
  Visibility,
  Star,
  Warning,
  CheckCircle,
  Info,
  Speed,
  Target,
  MonetizationOn,
  People,
  ShoppingCart,
  Store,
  Business
} from '@mui/icons-material';

const BusinessIntelligence = ({ storeId }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [biData, setBiData] = useState(null);
  const [forecasts, setForecasts] = useState([]);
  const [insights, setInsights] = useState([]);
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState('30d');

  useEffect(() => {
    fetchBusinessIntelligenceData();
  }, [storeId, dateRange]);

  const fetchBusinessIntelligenceData = async () => {
    try {
      setLoading(true);
      // Mock data for demonstration
      setBiData({
        kpis: {
          revenueGrowth: 12.5,
          customerGrowth: 8.3,
          profitMargin: 23.7,
          inventoryTurnover: 4.2,
          customerSatisfaction: 4.6,
          marketShare: 15.8
        },
        trends: {
          revenue: [
            { period: 'Jan', value: 45000, forecast: 48000 },
            { period: 'Feb', value: 52000, forecast: 55000 },
            { period: 'Mar', value: 48000, forecast: 51000 },
            { period: 'Apr', value: 61000, forecast: 64000 },
            { period: 'May', value: 58000, forecast: 62000 },
            { period: 'Jun', value: 67000, forecast: 71000 }
          ],
          customers: [
            { period: 'Jan', value: 1250, forecast: 1300 },
            { period: 'Feb', value: 1340, forecast: 1400 },
            { period: 'Mar', value: 1420, forecast: 1480 },
            { period: 'Apr', value: 1580, forecast: 1650 },
            { period: 'May', value: 1650, forecast: 1720 },
            { period: 'Jun', value: 1780, forecast: 1850 }
          ]
        },
        competitiveAnalysis: {
          marketPosition: 'Strong',
          competitorCount: 8,
          priceCompetitiveness: 'Above Average',
          uniqueSellingPoints: ['Self-checkout technology', 'Competitive pricing', 'Wide product range']
        }
      });

      setForecasts([
        {
          id: 1,
          type: 'Revenue',
          period: 'Next Quarter',
          prediction: 195000,
          confidence: 87,
          trend: 'up',
          factors: ['Seasonal increase', 'New product launches', 'Marketing campaigns']
        },
        {
          id: 2,
          type: 'Customer Growth',
          period: 'Next Month',
          prediction: 1950,
          confidence: 92,
          trend: 'up',
          factors: ['Loyalty program', 'Word of mouth', 'Local events']
        },
        {
          id: 3,
          type: 'Inventory Needs',
          period: 'Next Week',
          prediction: 'High demand for electronics',
          confidence: 78,
          trend: 'up',
          factors: ['Back to school season', 'Promotional campaigns']
        }
      ]);

      setInsights([
        {
          id: 1,
          type: 'opportunity',
          title: 'Peak Shopping Hours',
          description: 'Customer traffic is highest between 2-4 PM on weekdays. Consider staffing optimization.',
          impact: 'High',
          actionable: true
        },
        {
          id: 2,
          type: 'warning',
          title: 'Inventory Imbalance',
          description: 'Electronics category is overstocked while beverages are understocked.',
          impact: 'Medium',
          actionable: true
        },
        {
          id: 3,
          type: 'success',
          title: 'Customer Retention',
          description: 'Loyalty program has increased repeat customers by 23% this quarter.',
          impact: 'High',
          actionable: false
        },
        {
          id: 4,
          type: 'info',
          title: 'Seasonal Trend',
          description: 'Bakery products show 15% higher sales on weekends.',
          impact: 'Low',
          actionable: true
        }
      ]);

      setReports([
        {
          id: 1,
          name: 'Monthly Performance Report',
          type: 'Performance',
          lastGenerated: '2024-07-01',
          status: 'Ready',
          format: 'PDF'
        },
        {
          id: 2,
          name: 'Customer Behavior Analysis',
          type: 'Customer',
          lastGenerated: '2024-06-28',
          status: 'Ready',
          format: 'Excel'
        },
        {
          id: 3,
          name: 'Inventory Optimization Report',
          type: 'Inventory',
          lastGenerated: '2024-07-05',
          status: 'Generating',
          format: 'PDF'
        },
        {
          id: 4,
          name: 'Financial Summary',
          type: 'Financial',
          lastGenerated: '2024-07-08',
          status: 'Ready',
          format: 'PDF'
        }
      ]);

    } catch (error) {
      console.error('Error fetching business intelligence data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getInsightIcon = (type) => {
    switch (type) {
      case 'opportunity': return <TrendingUp color="success" />;
      case 'warning': return <Warning color="warning" />;
      case 'success': return <CheckCircle color="success" />;
      case 'info': return <Info color="info" />;
      default: return <Info />;
    }
  };

  const getInsightColor = (type) => {
    switch (type) {
      case 'opportunity': return 'success';
      case 'warning': return 'warning';
      case 'success': return 'success';
      case 'info': return 'info';
      default: return 'default';
    }
  };

  const MetricCard = ({ title, value, change, icon, color = 'primary', format = 'number', suffix = '' }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" color={color}>
              {format === 'currency' ? `R${value?.toLocaleString() || 0}` : 
               format === 'percentage' ? `${value || 0}%` : 
               value?.toLocaleString() || 0}{suffix}
            </Typography>
            {change !== undefined && (
              <Box display="flex" alignItems="center" mt={1}>
                {change >= 0 ? (
                  <TrendingUp color="success" fontSize="small" />
                ) : (
                  <TrendingDown color="error" fontSize="small" />
                )}
                <Typography
                  variant="body2"
                  color={change >= 0 ? 'success.main' : 'error.main'}
                  sx={{ ml: 0.5 }}
                >
                  {Math.abs(change)}%
                </Typography>
              </Box>
            )}
          </Box>
          <Avatar sx={{ bgcolor: `${color}.light` }}>
            {icon}
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <LinearProgress sx={{ width: '50%' }} />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Business Intelligence</Typography>
        <Box display="flex" gap={2}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={dateRange}
              label="Time Range"
              onChange={(e) => setDateRange(e.target.value)}
            >
              <MenuItem value="7d">Last 7 days</MenuItem>
              <MenuItem value="30d">Last 30 days</MenuItem>
              <MenuItem value="90d">Last 3 months</MenuItem>
              <MenuItem value="1y">Last year</MenuItem>
            </Select>
          </FormControl>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={fetchBusinessIntelligenceData}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<Download />}
          >
            Export Report
          </Button>
        </Box>
      </Box>

      {/* Key Performance Indicators */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Revenue Growth"
            value={biData?.kpis?.revenueGrowth}
            format="percentage"
            icon={<MonetizationOn />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Customer Growth"
            value={biData?.kpis?.customerGrowth}
            format="percentage"
            icon={<People />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Profit Margin"
            value={biData?.kpis?.profitMargin}
            format="percentage"
            icon={<Assessment />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Inventory Turnover"
            value={biData?.kpis?.inventoryTurnover}
            suffix="x"
            icon={<Speed />}
            color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Customer Satisfaction"
            value={biData?.kpis?.customerSatisfaction}
            suffix="/5"
            icon={<Star />}
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Market Share"
            value={biData?.kpis?.marketShare}
            format="percentage"
            icon={<Target />}
            color="primary"
          />
        </Grid>
      </Grid>

      {/* Main Content Tabs */}
      <Card>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab
            label="Insights"
            icon={<Insights />}
            iconPosition="start"
          />
          <Tab
            label="Forecasting"
            icon={<Forecast />}
            iconPosition="start"
          />
          <Tab
            label="Competitive Analysis"
            icon={<CompareArrows />}
            iconPosition="start"
          />
          <Tab
            label="Reports"
            icon={<Report />}
            iconPosition="start"
          />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {/* Insights Tab */}
          {activeTab === 0 && (
            <Box>
              <Typography variant="h6" mb={3}>Business Insights</Typography>

              <Grid container spacing={3}>
                {insights.map((insight) => (
                  <Grid item xs={12} md={6} key={insight.id}>
                    <Card>
                      <CardContent>
                        <Box display="flex" alignItems="flex-start" mb={2}>
                          <Box sx={{ mr: 2 }}>
                            {getInsightIcon(insight.type)}
                          </Box>
                          <Box flex={1}>
                            <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                              <Typography variant="h6">{insight.title}</Typography>
                              <Chip
                                label={insight.impact}
                                color={insight.impact === 'High' ? 'error' : insight.impact === 'Medium' ? 'warning' : 'default'}
                                size="small"
                              />
                            </Box>
                            <Typography variant="body2" color="textSecondary" mb={2}>
                              {insight.description}
                            </Typography>
                            {insight.actionable && (
                              <Button size="small" variant="outlined">
                                Take Action
                              </Button>
                            )}
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {/* Forecasting Tab */}
          {activeTab === 1 && (
            <Box>
              <Typography variant="h6" mb={3}>Predictive Analytics & Forecasting</Typography>

              <Grid container spacing={3}>
                {forecasts.map((forecast) => (
                  <Grid item xs={12} md={6} key={forecast.id}>
                    <Card>
                      <CardContent>
                        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                          <Typography variant="h6">{forecast.type}</Typography>
                          <Box display="flex" alignItems="center">
                            {forecast.trend === 'up' ? (
                              <TrendingUp color="success" />
                            ) : (
                              <TrendingDown color="error" />
                            )}
                          </Box>
                        </Box>

                        <Typography variant="body2" color="textSecondary" mb={1}>
                          {forecast.period}
                        </Typography>

                        <Typography variant="h4" color="primary" mb={2}>
                          {typeof forecast.prediction === 'number'
                            ? forecast.prediction.toLocaleString()
                            : forecast.prediction}
                        </Typography>

                        <Box display="flex" alignItems="center" mb={2}>
                          <Typography variant="body2" color="textSecondary" sx={{ mr: 1 }}>
                            Confidence:
                          </Typography>
                          <Typography variant="body2" fontWeight="bold">
                            {forecast.confidence}%
                          </Typography>
                        </Box>

                        <Typography variant="body2" color="textSecondary" mb={1}>
                          Key Factors:
                        </Typography>
                        <List dense>
                          {forecast.factors.map((factor, index) => (
                            <ListItem key={index} sx={{ py: 0 }}>
                              <ListItemText
                                primary={factor}
                                primaryTypographyProps={{ variant: 'body2' }}
                              />
                            </ListItem>
                          ))}
                        </List>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {/* Competitive Analysis Tab */}
          {activeTab === 2 && (
            <Box>
              <Typography variant="h6" mb={3}>Competitive Analysis</Typography>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Market Position</Typography>
                      <Typography variant="h4" color="success.main" mb={2}>
                        {biData?.competitiveAnalysis?.marketPosition}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Your store maintains a strong position in the local market with competitive advantages.
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Competitor Landscape</Typography>
                      <Typography variant="h4" color="primary" mb={2}>
                        {biData?.competitiveAnalysis?.competitorCount} Competitors
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Active competitors in your market area with similar offerings.
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Unique Selling Points</Typography>
                      <Grid container spacing={2}>
                        {biData?.competitiveAnalysis?.uniqueSellingPoints.map((usp, index) => (
                          <Grid item xs={12} sm={4} key={index}>
                            <Box display="flex" alignItems="center">
                              <CheckCircle color="success" sx={{ mr: 1 }} />
                              <Typography variant="body2">{usp}</Typography>
                            </Box>
                          </Grid>
                        ))}
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Reports Tab */}
          {activeTab === 3 && (
            <Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h6">Business Reports</Typography>
                <Button
                  variant="contained"
                  startIcon={<Report />}
                >
                  Generate New Report
                </Button>
              </Box>

              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Report Name</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Last Generated</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Format</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {reports.map((report) => (
                      <TableRow key={report.id}>
                        <TableCell>
                          <Typography variant="body2" fontWeight="bold">
                            {report.name}
                          </Typography>
                        </TableCell>
                        <TableCell>{report.type}</TableCell>
                        <TableCell>{report.lastGenerated}</TableCell>
                        <TableCell>
                          <Chip
                            label={report.status}
                            color={report.status === 'Ready' ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{report.format}</TableCell>
                        <TableCell>
                          <Box display="flex" gap={1}>
                            {report.status === 'Ready' && (
                              <IconButton size="small">
                                <Download />
                              </IconButton>
                            )}
                            <IconButton size="small">
                              <Visibility />
                            </IconButton>
                            <IconButton size="small">
                              <Refresh />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </Box>
      </Card>
    </Box>
  );
};

export default BusinessIntelligence;
