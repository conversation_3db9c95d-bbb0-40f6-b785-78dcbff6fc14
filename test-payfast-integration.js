const axios = require('axios');
const crypto = require('crypto');

// Test configuration
const BASE_URL = 'http://localhost:5000/api';
const TEST_STORE_ID = '67979fefb85fdbcf752738e6'; // Replace with actual store ID
const TEST_SYSTEM_ID = 'PIC-m6m3sbbw-YCN'; // Replace with actual system ID

// Test credentials (use sandbox credentials)
const TEST_ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123'
};

const TEST_PAYFAST_CONFIG = {
  merchantId: '10000100',
  merchantKey: '46f0cd694581a',
  passphrase: 'jt7NOE43FZPn',
  sandbox: true
};

let adminToken = '';
let storeToken = '';

/**
 * Test suite for PayFast integration
 */
class PayFastIntegrationTest {
  
  async runAllTests() {
    console.log('🚀 Starting PayFast Integration Tests...\n');
    
    try {
      // Step 1: Authentication tests
      await this.testAuthentication();
      
      // Step 2: Store PayFast configuration
      await this.testPayFastConfiguration();
      
      // Step 3: System authentication
      await this.testSystemAuthentication();
      
      // Step 4: Payment initiation
      await this.testPaymentInitiation();
      
      // Step 5: Payment status checking
      await this.testPaymentStatusCheck();
      
      // Step 6: Report generation
      await this.testReportGeneration();
      
      console.log('✅ All tests completed successfully!');
      
    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      process.exit(1);
    }
  }
  
  async testAuthentication() {
    console.log('📝 Testing Authentication...');
    
    try {
      // Test admin login
      const adminResponse = await axios.post(`${BASE_URL}/auth/login`, TEST_ADMIN_CREDENTIALS);
      adminToken = adminResponse.data.token;
      console.log('✅ Admin authentication successful');
      
      // Test system authentication
      const systemResponse = await axios.post(`${BASE_URL}/systems/auth`, {
        systemId: TEST_SYSTEM_ID,
        storeName: 'Test Store'
      });
      storeToken = systemResponse.data.token;
      console.log('✅ System authentication successful');
      
    } catch (error) {
      throw new Error(`Authentication failed: ${error.response?.data?.message || error.message}`);
    }
  }
  
  async testPayFastConfiguration() {
    console.log('📝 Testing PayFast Configuration...');
    
    try {
      // Configure PayFast for the test store
      const configResponse = await axios.put(
        `${BASE_URL}/stores/${TEST_STORE_ID}/payfast`,
        TEST_PAYFAST_CONFIG,
        {
          headers: { Authorization: `Bearer ${adminToken}` }
        }
      );
      
      console.log('✅ PayFast configuration successful');
      
      // Test PayFast connection
      const testResponse = await axios.get(
        `${BASE_URL}/stores/${TEST_STORE_ID}/payfast/test`,
        {
          headers: { Authorization: `Bearer ${adminToken}` }
        }
      );
      
      console.log('✅ PayFast connection test successful');
      
    } catch (error) {
      throw new Error(`PayFast configuration failed: ${error.response?.data?.message || error.message}`);
    }
  }
  
  async testSystemAuthentication() {
    console.log('📝 Testing System Authentication...');
    
    try {
      const response = await axios.post(`${BASE_URL}/systems/auth`, {
        systemId: TEST_SYSTEM_ID,
        storeName: 'Test Store'
      });
      
      if (!response.data.token) {
        throw new Error('No token received');
      }
      
      console.log('✅ System authentication successful');
      
    } catch (error) {
      throw new Error(`System authentication failed: ${error.response?.data?.message || error.message}`);
    }
  }
  
  async testPaymentInitiation() {
    console.log('📝 Testing Payment Initiation...');
    
    try {
      const paymentData = {
        storeId: TEST_STORE_ID,
        systemId: TEST_SYSTEM_ID,
        amount: 99.99,
        items: [
          {
            name: 'Test Product',
            price: 99.99,
            quantity: 1,
            barcode: '1234567890'
          }
        ],
        paymentMethod: 'card'
      };
      
      const response = await axios.post(
        `${BASE_URL}/payments/initiate`,
        paymentData,
        {
          headers: { Authorization: `Bearer ${storeToken}` }
        }
      );
      
      if (!response.data.success || !response.data.transactionId) {
        throw new Error('Payment initiation failed');
      }
      
      this.testTransactionId = response.data.transactionId;
      console.log('✅ Payment initiation successful');
      console.log(`   Transaction ID: ${this.testTransactionId}`);
      
    } catch (error) {
      throw new Error(`Payment initiation failed: ${error.response?.data?.message || error.message}`);
    }
  }
  
  async testPaymentStatusCheck() {
    console.log('📝 Testing Payment Status Check...');
    
    try {
      const response = await axios.get(
        `${BASE_URL}/payments/status/${this.testTransactionId}`,
        {
          headers: { Authorization: `Bearer ${storeToken}` }
        }
      );
      
      if (!response.data.transactionId) {
        throw new Error('Invalid payment status response');
      }
      
      console.log('✅ Payment status check successful');
      console.log(`   Status: ${response.data.status}`);
      
    } catch (error) {
      throw new Error(`Payment status check failed: ${error.response?.data?.message || error.message}`);
    }
  }
  
  async testReportGeneration() {
    console.log('📝 Testing Report Generation...');
    
    try {
      // Test store reports
      const storeReportResponse = await axios.get(
        `${BASE_URL}/reports/stores/${TEST_STORE_ID}?timeframe=week`,
        {
          headers: { Authorization: `Bearer ${adminToken}` }
        }
      );
      
      console.log('✅ Store report generation successful');
      
      // Test admin reports
      const adminReportResponse = await axios.get(
        `${BASE_URL}/reports/admin?timeframe=week`,
        {
          headers: { Authorization: `Bearer ${adminToken}` }
        }
      );
      
      console.log('✅ Admin report generation successful');
      
    } catch (error) {
      throw new Error(`Report generation failed: ${error.response?.data?.message || error.message}`);
    }
  }
  
  async testWebhookSimulation() {
    console.log('📝 Testing Webhook Simulation...');
    
    try {
      // Simulate a PayFast webhook
      const webhookData = {
        m_payment_id: this.testTransactionId,
        pf_payment_id: '1234567',
        payment_status: 'COMPLETE',
        amount_gross: '99.99',
        custom_str1: this.testTransactionId,
        custom_str2: TEST_STORE_ID,
        custom_str3: TEST_SYSTEM_ID
      };
      
      // Generate signature (simplified for testing)
      const signature = crypto.createHash('md5')
        .update(Object.values(webhookData).join(''))
        .digest('hex');
      
      webhookData.signature = signature;
      
      const response = await axios.post(`${BASE_URL}/payments/webhook`, webhookData);
      
      console.log('✅ Webhook simulation successful');
      
    } catch (error) {
      console.log('⚠️  Webhook simulation failed (expected in test environment)');
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const testSuite = new PayFastIntegrationTest();
  testSuite.runAllTests().catch(console.error);
}

module.exports = PayFastIntegrationTest;
