const Transaction = require('../models/Transaction');
const Store = require('../models/Store');
const Product = require('../models/Product');

/**
 * Get store-specific reports
 */
exports.getStoreReports = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { timeframe = 'week' } = req.query;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Calculate date range based on timeframe
    const now = new Date();
    let startDate;
    
    switch (timeframe) {
      case 'day':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // Get sales data
    const salesAggregation = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: null,
          totalSales: { $sum: '$amount' },
          totalTransactions: { $sum: 1 },
          averageOrder: { $avg: '$amount' }
        }
      }
    ]);

    const salesData = salesAggregation[0] || {
      totalSales: 0,
      totalTransactions: 0,
      averageOrder: 0
    };

    // Get top products
    const topProductsAggregation = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      { $unwind: '$items' },
      {
        $group: {
          _id: '$items.name',
          totalQuantity: { $sum: '$items.quantity' },
          totalRevenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } },
          barcode: { $first: '$items.barcode' }
        }
      },
      { $sort: { totalQuantity: -1 } },
      { $limit: 10 }
    ]);

    // Get daily sales trend
    const dailySalesAggregation = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          dailySales: { $sum: '$amount' },
          dailyTransactions: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    res.status(200).json({
      sales: {
        total: salesData.totalSales,
        transactions: salesData.totalTransactions,
        averageOrder: salesData.averageOrder
      },
      topProducts: topProductsAggregation,
      dailySales: dailySalesAggregation,
      timeframe,
      dateRange: {
        start: startDate,
        end: now
      }
    });

  } catch (error) {
    console.error('Store reports error:', error);
    res.status(500).json({ 
      message: 'Failed to generate store reports', 
      error: error.message 
    });
  }
};

/**
 * Get admin reports (cross-store analytics)
 */
exports.getAdminReports = async (req, res) => {
  try {
    const { timeframe = 'month', store = 'all' } = req.query;

    // Only admins can access this endpoint
    if (req.user.role !== "admin") {
      return res.status(403).json({ message: "Admin access required" });
    }

    // Calculate date range
    const now = new Date();
    let startDate;
    
    switch (timeframe) {
      case 'day':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    }

    // Build match criteria
    const matchCriteria = {
      status: 'completed',
      createdAt: { $gte: startDate }
    };

    if (store !== 'all') {
      matchCriteria.storeId = require('mongoose').Types.ObjectId(store);
    }

    // Get overall sales data
    const overallSalesAggregation = await Transaction.aggregate([
      { $match: matchCriteria },
      {
        $group: {
          _id: null,
          totalSales: { $sum: '$amount' },
          totalTransactions: { $sum: 1 },
          averageOrder: { $avg: '$amount' }
        }
      }
    ]);

    const overallSales = overallSalesAggregation[0] || {
      totalSales: 0,
      totalTransactions: 0,
      averageOrder: 0
    };

    // Get store performance breakdown
    const storePerformanceAggregation = await Transaction.aggregate([
      { $match: matchCriteria },
      {
        $group: {
          _id: '$storeId',
          sales: { $sum: '$amount' },
          transactions: { $sum: 1 },
          averageOrder: { $avg: '$amount' }
        }
      },
      {
        $lookup: {
          from: 'stores',
          localField: '_id',
          foreignField: '_id',
          as: 'store'
        }
      },
      {
        $unwind: '$store'
      },
      {
        $project: {
          _id: 1,
          name: '$store.name',
          sales: 1,
          transactions: 1,
          averageOrder: 1
        }
      },
      { $sort: { sales: -1 } }
    ]);

    // Get payment method breakdown
    const paymentMethodAggregation = await Transaction.aggregate([
      { $match: matchCriteria },
      {
        $group: {
          _id: '$payfast.paymentMethod',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get top performing products across all stores
    const topProductsAggregation = await Transaction.aggregate([
      { $match: matchCriteria },
      { $unwind: '$items' },
      {
        $group: {
          _id: '$items.name',
          totalQuantity: { $sum: '$items.quantity' },
          totalRevenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } },
          storeCount: { $addToSet: '$storeId' }
        }
      },
      {
        $addFields: {
          storeCount: { $size: '$storeCount' }
        }
      },
      { $sort: { totalRevenue: -1 } },
      { $limit: 20 }
    ]);

    res.status(200).json({
      totalSales: overallSales.totalSales,
      totalTransactions: overallSales.totalTransactions,
      averageOrder: overallSales.averageOrder,
      storePerformance: storePerformanceAggregation,
      paymentMethods: paymentMethodAggregation,
      topProducts: topProductsAggregation,
      timeframe,
      storeFilter: store,
      dateRange: {
        start: startDate,
        end: now
      }
    });

  } catch (error) {
    console.error('Admin reports error:', error);
    res.status(500).json({ 
      message: 'Failed to generate admin reports', 
      error: error.message 
    });
  }
};

/**
 * Get transaction details for a specific transaction
 */
exports.getTransactionDetails = async (req, res) => {
  try {
    const { transactionId } = req.params;

    const transaction = await Transaction.findOne({ transactionId })
      .populate('storeId', 'name email')
      .populate('items.productId', 'name barcode image');

    if (!transaction) {
      return res.status(404).json({ message: 'Transaction not found' });
    }

    // Check access permissions
    if (req.user.role === "store" && req.user.storeId.toString() !== transaction.storeId._id.toString()) {
      return res.status(403).json({ message: "Access denied" });
    }

    res.status(200).json({
      transaction: {
        transactionId: transaction.transactionId,
        amount: transaction.amount,
        currency: transaction.currency,
        status: transaction.status,
        items: transaction.items,
        store: transaction.storeId,
        payfast: transaction.payfast,
        receipt: transaction.receipt,
        createdAt: transaction.createdAt,
        completedAt: transaction.completedAt
      }
    });

  } catch (error) {
    console.error('Transaction details error:', error);
    res.status(500).json({ 
      message: 'Failed to get transaction details', 
      error: error.message 
    });
  }
};

/**
 * Export sales data to CSV
 */
exports.exportSalesData = async (req, res) => {
  try {
    const { storeId, startDate, endDate, format = 'csv' } = req.query;

    // Check permissions
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    const matchCriteria = {
      status: 'completed'
    };

    if (storeId && storeId !== 'all') {
      matchCriteria.storeId = require('mongoose').Types.ObjectId(storeId);
    }

    if (startDate && endDate) {
      matchCriteria.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    const transactions = await Transaction.find(matchCriteria)
      .populate('storeId', 'name')
      .sort({ createdAt: -1 });

    // For now, return JSON data
    // In production, you would generate actual CSV/Excel files
    res.status(200).json({
      message: 'Sales data export',
      format,
      count: transactions.length,
      data: transactions.map(t => ({
        transactionId: t.transactionId,
        store: t.storeId.name,
        amount: t.amount,
        currency: t.currency,
        status: t.status,
        itemCount: t.items.length,
        paymentMethod: t.payfast?.paymentMethod || 'unknown',
        createdAt: t.createdAt,
        completedAt: t.completedAt
      }))
    });

  } catch (error) {
    console.error('Export sales data error:', error);
    res.status(500).json({ 
      message: 'Failed to export sales data', 
      error: error.message 
    });
  }
};
