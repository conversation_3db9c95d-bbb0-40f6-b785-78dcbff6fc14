#!/bin/bash
# Self-Checkout Raspberry Pi Installation Script
# Usage: chmod +x install-pi.sh && ./install-pi.sh

set -e  # Exit on any error

echo "🍓 Setting up Raspberry Pi for Self-Checkout System..."

# Check if running on Raspberry Pi
if ! grep -q "Raspberry Pi" /proc/cpuinfo 2>/dev/null; then
    echo "⚠️  This script is designed for Raspberry Pi. Continuing anyway..."
fi

# Check if running as pi user
if [[ "$USER" != "pi" ]]; then
    echo "⚠️  This script is designed to run as 'pi' user. Current user: $USER"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo "📋 System Information:"
echo "   OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
echo "   Architecture: $(uname -m)"
echo "   User: $USER"
echo "   Home: $HOME"

# Update system
echo "🔄 Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install system dependencies
echo "📦 Installing system dependencies..."
sudo apt install -y \
    python3-pip \
    python3-venv \
    python3-dev \
    python3-opencv \
    python3-tk \
    libzbar0 \
    libzbar-dev \
    git \
    curl \
    vim \
    htop

# Install additional libraries for camera and display
sudo apt install -y \
    libatlas-base-dev \
    libhdf5-dev \
    libhdf5-serial-dev \
    libatlas-base-dev \
    libjasper-dev \
    libqtgui4 \
    libqt4-test

# Enable camera interface
echo "📷 Enabling camera interface..."
sudo raspi-config nonint do_camera 0

# Enable SPI and I2C (for potential hardware expansions)
echo "🔧 Enabling SPI and I2C interfaces..."
sudo raspi-config nonint do_spi 0
sudo raspi-config nonint do_i2c 0

# Create project directory
PROJECT_DIR="$HOME/selfcheckout"
echo "📁 Creating project directory: $PROJECT_DIR"
mkdir -p "$PROJECT_DIR"
cd "$PROJECT_DIR"

# Create virtual environment
echo "🐍 Creating Python virtual environment..."
python3 -m venv selfcheckout_env

# Activate virtual environment
source selfcheckout_env/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install Python packages
echo "📦 Installing Python packages..."
pip install \
    opencv-python==******** \
    pyzbar==0.1.9 \
    ttkbootstrap==1.10.1 \
    requests==2.31.0 \
    pillow==10.0.1 \
    numpy==1.24.3

# Test camera functionality
echo "🔍 Testing camera functionality..."
if python3 -c "
import cv2
cap = cv2.VideoCapture(0)
if cap.isOpened():
    print('✅ Camera test successful')
    cap.release()
else:
    print('❌ Camera test failed')
    exit(1)
" 2>/dev/null; then
    echo "✅ Camera test passed"
else
    echo "⚠️  Camera test failed. Please check camera connection and enable camera interface."
fi

# Create system configuration template
echo "📝 Creating system configuration template..."
cat > system_config.json << EOF
{
    "system_id": "STORE1-TROLLEY-001",
    "store_name": "Your Store Name",
    "store_id": "your_mongodb_store_id",
    "backend_url": "http://your-server-ip:5000/api"
}
EOF

# Create systemd service for auto-start
echo "⚙️  Creating systemd service..."
sudo tee /etc/systemd/system/selfcheckout.service > /dev/null << EOF
[Unit]
Description=Self Checkout System
After=network.target graphical-session.target
Wants=graphical-session.target

[Service]
Type=simple
User=pi
Group=pi
WorkingDirectory=$PROJECT_DIR
Environment=DISPLAY=:0
Environment=XAUTHORITY=/home/<USER>/.Xauthority
ExecStartPre=/bin/sleep 10
ExecStart=$PROJECT_DIR/selfcheckout_env/bin/python $PROJECT_DIR/sys2.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=graphical-session.target
EOF

# Enable the service (but don't start it yet)
sudo systemctl daemon-reload
sudo systemctl enable selfcheckout.service

# Configure automatic login (optional)
echo "🔐 Configuring automatic login..."
sudo raspi-config nonint do_boot_behaviour B4  # Desktop autologin

# Install CUPS for printer support
echo "🖨️  Installing printer support..."
sudo apt install -y cups cups-client
sudo usermod -a -G lpadmin pi

# Configure display settings for touchscreen
echo "📺 Configuring display settings..."
if ! grep -q "hdmi_force_hotplug=1" /boot/config.txt; then
    echo "hdmi_force_hotplug=1" | sudo tee -a /boot/config.txt
fi

if ! grep -q "hdmi_drive=2" /boot/config.txt; then
    echo "hdmi_drive=2" | sudo tee -a /boot/config.txt
fi

# Disable screen blanking
echo "💡 Disabling screen blanking..."
if ! grep -q "xserver-command=X -s 0 dpms" /etc/lightdm/lightdm.conf; then
    sudo sed -i 's/#xserver-command=X/xserver-command=X -s 0 dpms/' /etc/lightdm/lightdm.conf
fi

# Create startup script
echo "🚀 Creating startup script..."
cat > start_selfcheckout.sh << 'EOF'
#!/bin/bash
cd /home/<USER>/selfcheckout
source selfcheckout_env/bin/activate
python sys2.py
EOF

chmod +x start_selfcheckout.sh

# Create stop script
cat > stop_selfcheckout.sh << 'EOF'
#!/bin/bash
sudo systemctl stop selfcheckout.service
EOF

chmod +x stop_selfcheckout.sh

# Create status script
cat > status_selfcheckout.sh << 'EOF'
#!/bin/bash
echo "🔍 Self-Checkout System Status:"
echo "================================"
sudo systemctl status selfcheckout.service --no-pager
echo ""
echo "📊 System Resources:"
echo "CPU: $(vcgencmd measure_temp)"
echo "Memory: $(free -h | grep Mem | awk '{print $3 "/" $2}')"
echo "Disk: $(df -h / | tail -1 | awk '{print $3 "/" $2 " (" $5 " used)"}')"
echo ""
echo "📷 Camera Status:"
vcgencmd get_camera
EOF

chmod +x status_selfcheckout.sh

# Create log viewer script
cat > view_logs.sh << 'EOF'
#!/bin/bash
echo "📋 Self-Checkout System Logs:"
echo "============================="
sudo journalctl -u selfcheckout.service -f
EOF

chmod +x view_logs.sh

echo ""
echo "🎉 Raspberry Pi setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Copy sys2.py to $PROJECT_DIR/"
echo "2. Edit system_config.json with your store configuration"
echo "3. Test the application: ./start_selfcheckout.sh"
echo "4. Enable auto-start: sudo systemctl start selfcheckout.service"
echo ""
echo "🔧 Useful commands:"
echo "   Start:  sudo systemctl start selfcheckout.service"
echo "   Stop:   sudo systemctl stop selfcheckout.service"
echo "   Status: ./status_selfcheckout.sh"
echo "   Logs:   ./view_logs.sh"
echo ""
echo "📁 Project directory: $PROJECT_DIR"
echo "⚙️  Service file: /etc/systemd/system/selfcheckout.service"
echo "📝 Configuration: $PROJECT_DIR/system_config.json"
echo ""
echo "⚠️  Please reboot the system to ensure all changes take effect:"
echo "   sudo reboot"
echo ""
