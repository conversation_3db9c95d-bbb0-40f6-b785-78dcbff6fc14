version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: selfcheckout-db
    restart: always
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD:-password123}
      MONGO_INITDB_DATABASE: selfcheckout
    networks:
      - selfcheckout-network

  # Backend API
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: selfcheckout-api
    restart: always
    ports:
      - "5000:5000"
    depends_on:
      - mongodb
    environment:
      MONGODB_URI: mongodb://admin:${MONGO_PASSWORD:-password123}@mongodb:27017/selfcheckout?authSource=admin
      JWT_SECRET: ${JWT_SECRET:-your_jwt_secret_here_please_change_this}
      NODE_ENV: production
      PORT: 5000
      BASE_URL: ${BASE_URL:-http://localhost:5000}
      MAX_FILE_SIZE: 5242880
      UPLOAD_PATH: /app/uploads
      PAYFAST_SANDBOX: ${PAYFAST_SANDBOX:-true}
    volumes:
      - ./backend/uploads:/app/uploads
      - backend_logs:/app/logs
    networks:
      - selfcheckout-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Web Application
  frontend:
    build:
      context: ./ui
      dockerfile: Dockerfile
    container_name: selfcheckout-web
    restart: always
    ports:
      - "80:80"
    depends_on:
      - backend
    environment:
      VITE_API_URL: ${API_URL:-http://localhost:5000/api}
    networks:
      - selfcheckout-network

  # Redis for session management and caching (optional)
  redis:
    image: redis:7-alpine
    container_name: selfcheckout-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - selfcheckout-network
    command: redis-server --appendonly yes

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: selfcheckout-proxy
    restart: always
    ports:
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - selfcheckout-network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local
  nginx_logs:
    driver: local

networks:
  selfcheckout-network:
    driver: bridge
