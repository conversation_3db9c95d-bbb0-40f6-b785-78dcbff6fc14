#!/bin/bash
# Master Installation Script for Self-Checkout System
# Usage: chmod +x install.sh && ./install.sh

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    printf "${1}${2}${NC}\n"
}

print_header() {
    echo ""
    print_color $CYAN "=================================="
    print_color $CYAN "$1"
    print_color $CYAN "=================================="
    echo ""
}

print_success() {
    print_color $GREEN "✅ $1"
}

print_warning() {
    print_color $YELLOW "⚠️  $1"
}

print_error() {
    print_color $RED "❌ $1"
}

print_info() {
    print_color $BLUE "ℹ️  $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root"
   exit 1
fi

print_header "🛒 Self-Checkout System Installation"

print_info "This script will install the complete self-checkout system:"
print_info "• Backend API (Node.js + Express + MongoDB)"
print_info "• Frontend Web App (React + Vite)"
print_info "• PayFast POS Integration"
print_info "• Docker deployment configuration"
echo ""

# Detect OS
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
    DISTRO=$(lsb_release -si 2>/dev/null || echo "Unknown")
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
    DISTRO="macOS"
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    OS="windows"
    DISTRO="Windows"
else
    print_error "Unsupported operating system: $OSTYPE"
    exit 1
fi

print_info "Detected OS: $DISTRO ($OS)"

# Check for required commands
check_command() {
    if command -v $1 &> /dev/null; then
        print_success "$1 is available"
        return 0
    else
        print_warning "$1 is not installed"
        return 1
    fi
}

print_header "🔍 Checking Prerequisites"

# Check for Git
if ! check_command git; then
    print_error "Git is required but not installed. Please install Git first."
    exit 1
fi

# Check for curl
if ! check_command curl; then
    print_error "curl is required but not installed. Please install curl first."
    exit 1
fi

# Installation options
print_header "📋 Installation Options"

echo "Please select installation type:"
echo "1) Full Installation (Backend + Frontend + Docker)"
echo "2) Backend Only"
echo "3) Frontend Only"
echo "4) Raspberry Pi Setup"
echo "5) Docker Deployment"
echo ""

read -p "Enter your choice (1-5): " INSTALL_TYPE

case $INSTALL_TYPE in
    1)
        INSTALL_BACKEND=true
        INSTALL_FRONTEND=true
        INSTALL_DOCKER=true
        ;;
    2)
        INSTALL_BACKEND=true
        INSTALL_FRONTEND=false
        INSTALL_DOCKER=false
        ;;
    3)
        INSTALL_BACKEND=false
        INSTALL_FRONTEND=true
        INSTALL_DOCKER=false
        ;;
    4)
        print_info "Raspberry Pi setup will be handled separately"
        print_info "Please run: chmod +x scripts/install-pi.sh && ./scripts/install-pi.sh"
        exit 0
        ;;
    5)
        INSTALL_BACKEND=false
        INSTALL_FRONTEND=false
        INSTALL_DOCKER=true
        ;;
    *)
        print_error "Invalid choice. Exiting."
        exit 1
        ;;
esac

# Create environment file
print_header "⚙️  Environment Configuration"

if [[ ! -f ".env" ]]; then
    print_info "Creating environment configuration..."
    
    # Generate JWT secret
    if command -v openssl &> /dev/null; then
        JWT_SECRET=$(openssl rand -base64 32)
    elif command -v python3 &> /dev/null; then
        JWT_SECRET=$(python3 -c "import secrets; print(secrets.token_urlsafe(32))")
    else
        JWT_SECRET="your_jwt_secret_here_please_change_this_to_something_secure"
        print_warning "Could not generate JWT secret automatically. Please update .env file."
    fi
    
    # Prompt for MongoDB URI
    read -p "Enter MongoDB URI (default: mongodb://localhost:27017/selfcheckout): " MONGODB_URI
    MONGODB_URI=${MONGODB_URI:-"mongodb://localhost:27017/selfcheckout"}
    
    # Create .env file
    cat > .env << EOF
# Self-Checkout System Configuration
MONGODB_URI=$MONGODB_URI
JWT_SECRET=$JWT_SECRET
PORT=5000
NODE_ENV=development
BASE_URL=http://localhost:5000
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads
PAYFAST_SANDBOX=true

# Docker Configuration
MONGO_PASSWORD=password123
API_URL=http://localhost:5000/api
EOF
    
    print_success "Environment file created"
else
    print_success "Environment file already exists"
fi

# Backend Installation
if [[ "$INSTALL_BACKEND" == true ]]; then
    print_header "🔧 Installing Backend"
    
    if [[ -d "backend" ]]; then
        cd backend
        
        # Make script executable and run
        chmod +x ../scripts/install-backend.sh
        ../scripts/install-backend.sh
        
        cd ..
        print_success "Backend installation completed"
    else
        print_error "Backend directory not found"
        exit 1
    fi
fi

# Frontend Installation
if [[ "$INSTALL_FRONTEND" == true ]]; then
    print_header "🌐 Installing Frontend"
    
    if [[ -d "ui" ]]; then
        cd ui
        
        # Make script executable and run
        chmod +x ../scripts/install-frontend.sh
        ../scripts/install-frontend.sh
        
        cd ..
        print_success "Frontend installation completed"
    else
        print_error "Frontend directory not found"
        exit 1
    fi
fi

# Docker Setup
if [[ "$INSTALL_DOCKER" == true ]]; then
    print_header "🐳 Setting up Docker Deployment"
    
    # Check if Docker is installed
    if ! check_command docker; then
        print_warning "Docker is not installed. Please install Docker first:"
        print_info "Visit: https://docs.docker.com/get-docker/"
    else
        # Check if Docker Compose is available
        if command -v docker-compose &> /dev/null || docker compose version &> /dev/null 2>&1; then
            print_success "Docker Compose is available"
            
            # Create Docker environment file
            if [[ ! -f ".env.docker" ]]; then
                cp .env .env.docker
                print_success "Docker environment file created"
            fi
            
            print_info "Docker setup completed. To start with Docker:"
            print_info "  docker-compose up -d"
            print_info "  or"
            print_info "  docker compose up -d"
        else
            print_warning "Docker Compose is not available"
        fi
    fi
fi

# Create useful scripts
print_header "📝 Creating Utility Scripts"

# Create start script
cat > start.sh << 'EOF'
#!/bin/bash
# Start Self-Checkout System

echo "🚀 Starting Self-Checkout System..."

# Start backend
if [[ -d "backend" ]]; then
    echo "Starting backend..."
    cd backend
    npm start &
    BACKEND_PID=$!
    cd ..
fi

# Start frontend
if [[ -d "ui" ]]; then
    echo "Starting frontend..."
    cd ui
    npm run dev &
    FRONTEND_PID=$!
    cd ..
fi

echo "✅ System started!"
echo "Backend: http://localhost:5000"
echo "Frontend: http://localhost:5173"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for interrupt
trap 'echo "Stopping services..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit' INT
wait
EOF

chmod +x start.sh

# Create stop script
cat > stop.sh << 'EOF'
#!/bin/bash
# Stop Self-Checkout System

echo "🛑 Stopping Self-Checkout System..."

# Kill Node.js processes
pkill -f "node.*server.js" 2>/dev/null || true
pkill -f "npm.*start" 2>/dev/null || true
pkill -f "npm.*dev" 2>/dev/null || true

echo "✅ System stopped!"
EOF

chmod +x stop.sh

# Create status script
cat > status.sh << 'EOF'
#!/bin/bash
# Check Self-Checkout System Status

echo "📊 Self-Checkout System Status"
echo "=============================="

# Check backend
if curl -s http://localhost:5000/api/health > /dev/null 2>&1; then
    echo "✅ Backend: Running (http://localhost:5000)"
else
    echo "❌ Backend: Not running"
fi

# Check frontend
if curl -s http://localhost:5173 > /dev/null 2>&1; then
    echo "✅ Frontend: Running (http://localhost:5173)"
else
    echo "❌ Frontend: Not running"
fi

# Check MongoDB
if command -v mongo &> /dev/null; then
    if mongo --eval "db.adminCommand('ismaster')" > /dev/null 2>&1; then
        echo "✅ MongoDB: Running"
    else
        echo "❌ MongoDB: Not running"
    fi
else
    echo "⚠️  MongoDB: Command not found"
fi

echo ""
echo "🔧 Useful commands:"
echo "  Start:  ./start.sh"
echo "  Stop:   ./stop.sh"
echo "  Status: ./status.sh"
EOF

chmod +x status.sh

print_success "Utility scripts created"

# Final instructions
print_header "🎉 Installation Complete!"

print_success "Self-Checkout System has been installed successfully!"
echo ""

print_info "📋 Next Steps:"

if [[ "$INSTALL_BACKEND" == true ]]; then
    print_info "1. Review backend configuration in backend/.env"
    print_info "2. Initialize database: cd backend && npm run seed"
fi

if [[ "$INSTALL_FRONTEND" == true ]]; then
    print_info "3. Review frontend configuration in ui/.env.local"
fi

print_info "4. Start the system: ./start.sh"
print_info "5. Access the web interface: http://localhost:5173"
print_info "6. <NAME_EMAIL> / admin123"

echo ""
print_info "📚 Documentation:"
print_info "• Installation Guide: INSTALLATION_GUIDE.md"
print_info "• PayFast Setup: docs/payfast-setup-guide.md"
print_info "• Architecture: docs/payfast-integration-architecture.md"

echo ""
print_info "🔧 Useful Commands:"
print_info "• Start system: ./start.sh"
print_info "• Stop system: ./stop.sh"
print_info "• Check status: ./status.sh"
print_info "• Docker deployment: docker-compose up -d"

echo ""
print_info "🍓 For Raspberry Pi setup:"
print_info "• Run: chmod +x scripts/install-pi.sh && ./scripts/install-pi.sh"

echo ""
print_success "Happy self-checkout! 🛒✨"
