import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *  # Import constants for styles
from PIL import Image, ImageTk
import cv2
import requests
import threading
import json
import numpy as np
import io
from urllib.request import urlopen
import os
import win32print
from datetime import datetime
from pyzbar import pyzbar

class SelfCheckoutSystem:
    def __init__(self, root):
        # Convert root to ttkbootstrap style
        self.root = ttk.Window(themename="cosmo")  # You can choose different themes like "cosmo", "darkly", "litera", etc
        self.root.title("Self-Checkout System")
        
        # Make the application fullscreen
        self.root.attributes('-fullscreen', True)
        
        # Add escape key binding to exit fullscreen (optional)
        self.root.bind('<Escape>', lambda e: self.root.attributes('-fullscreen', False))
        
        # Get screen dimensions
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # Backend URLs
        self.base_url = "http://localhost:5000/api"
        self.store_id = "67979fefb85fdbcf752738e3"  # Replace with actual store ID
        
        # Initialize frames
        self.home_frame = None
        self.scanner_frame = None
        self.payment_frame = None
        
        # Initialize camera
        self.cap = None
        self.running = False
        
        # Shopping data
        self.detected_barcodes = set()
        self.total_price = 0
        self.scanned_items = []
        
        # System authentication details
        self.system_id = None
        self.auth_token = None
        
        # Authenticate system before showing home screen
        self.authenticate_system()

    def authenticate_system(self):
        """Authenticate the system with the backend server."""
        try:
            # Read system ID from configuration file
            config_path = "system_config.json"
            if not os.path.exists(config_path):
                self.display_error("Configuration file not found. Please create system_config.json")
                return

            with open(config_path, 'r') as f:
                config = json.load(f)
                system_id = config.get('system_id')
                store_name = config.get('store_name')

            # Debug print
            print(f"System ID: {system_id}")
            print(f"Store name: {store_name}")

            if not system_id:
                self.display_error("System ID not found in configuration")
                return
            
            if not store_name:
                self.display_error("Store name not found in configuration")
                return

            # Make authentication request to backend
            response = requests.post(
                f"{self.base_url}/systems/auth",
                json={
                    "systemId": system_id,
                    "storeName": store_name
                },
                headers={
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            )
            
            # Debug prints
            print(f"Auth response status: {response.status_code}")
            print(f"Auth response headers: {response.headers}")
            
            if response.ok:
                data = response.json()
                if not data.get('token'):
                    self.display_error("Authentication successful but no token received")
                    return
                    
                self.system_id = system_id
                self.auth_token = data.get('token')
                self.store_id = data.get('storeId')
                
                # Debug print
                print(f"Authentication successful. Token received: {self.auth_token[:20]}...")
                
                self.show_home_screen()
            else:
                self.handle_auth_error(response)
                
        except Exception as e:
            print(f"Authentication error: {str(e)}")
            self.display_error(f"Authentication error: {str(e)}")

    def display_error(self, message):
        """Display error message to user."""
        messagebox.showerror("Error", message)

    def show_home_screen(self):
        """Display the home screen with store flyer and start button."""
        # Clear other frames if they exist
        if self.scanner_frame:
            self.scanner_frame.pack_forget()
        if self.payment_frame:
            self.payment_frame.pack_forget()

        # Create home frame if it doesn't exist
        if not self.home_frame:
            self.home_frame = ttk.Frame(self.root, bootstyle="default")
            
            # Store name label
            self.store_name_label = ttk.Label(
                self.home_frame, 
                text="Self Checkout",
                font=("Arial", 24, "bold"),
                bootstyle="default"
            )
            self.store_name_label.pack(pady=20)
            
            # Create a container frame for flyer and button
            self.flyer_container = ttk.Frame(self.home_frame, bootstyle="default")
            self.flyer_container.pack(fill="both", expand=True)
            
            # Flyer display
            self.flyer_label = ttk.Label(self.flyer_container, bootstyle="default")
            self.flyer_label.pack(fill="both", expand=True)
            
            # Start scanning button - positioned at bottom
            self.start_button = ttk.Button(
                self.home_frame,
                text="Start Scanning",
                command=self.show_scanner_screen,
                bootstyle="primary",
                padding=(40, 20)
            )
            self.start_button.pack(pady=(0, 30))

        self.home_frame.pack(fill="both", expand=True)
        self.fetch_store_flyer()

    def fetch_store_flyer(self):
        """Fetch and display the store flyer from backend."""
        try:
            # First get the flyer path
            response = requests.get(
                f"{self.base_url}/stores/{self.store_id}/flyer",
                headers={
                    "Authorization": f"Bearer {self.auth_token}",
                    "Accept": "application/json"
                }
            )
            
            print(f"Fetching flyer - URL: {self.base_url}/stores/{self.store_id}/flyer")
            print(f"Response status: {response.status_code}")
            
            if response.status_code == 404:
                # No flyer available - show default message
                if hasattr(self, 'flyer_label'):
                    self.flyer_label.config(
                        text="Welcome! No current promotions available.", 
                        font=("Arial", 16)
                    )
                return
            
            if not response.ok:
                error_msg = "Failed to fetch flyer information"
                try:
                    error_data = response.json()
                    if 'message' in error_data:
                        error_msg = error_data['message']
                except:
                    pass
                raise Exception(error_msg)
            
            data = response.json()
            flyer_path = data.get('flyer')
            
            if not flyer_path:
                if hasattr(self, 'flyer_label'):
                    self.flyer_label.config(
                        text="Welcome! No current promotions available.", 
                        font=("Arial", 16)
                    )
                return
            
            # Fix path separators and construct URL
            flyer_path = flyer_path.replace('\\', '/')  # Replace backslashes with forward slashes
            image_url = f"http://localhost:5000/{flyer_path}"
            print(f"Fetching flyer image from: {image_url}")
            
            image_response = requests.get(
                image_url,
                headers={"Authorization": f"Bearer {self.auth_token}"},
                stream=True
            )
            
            if not image_response.ok:
                raise Exception(f"Failed to download flyer image (Status: {image_response.status_code})")
            
            # Save the flyer temporarily
            temp_path = "temp_flyer.jpg"
            with open(temp_path, 'wb') as f:
                for chunk in image_response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            # Display the flyer
            self.display_flyer(temp_path)
            
            # Clean up temp file
            try:
                os.remove(temp_path)
            except:
                pass
            
        except Exception as e:
            print(f"Error fetching flyer: {str(e)}")  # Debug logging
            self.display_error(f"Error fetching flyer: {str(e)}")
            # Show a default message when flyer can't be loaded
            if hasattr(self, 'flyer_label'):
                self.flyer_label.config(
                    text="Welcome to our store!", 
                    font=("Arial", 20, "bold")
                )

    def display_flyer(self, image_path):
        """Display the flyer image in the GUI."""
        try:
            # Load image
            image = Image.open(image_path)
            
            # Get screen dimensions
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            
            # Calculate new dimensions maintaining aspect ratio
            display_width = screen_width
            ratio = display_width / image.width
            display_height = int(image.height * ratio)
            
            # If height is too tall, scale based on height instead
            if display_height > screen_height * 0.8:  # Use 80% of screen height
                display_height = int(screen_height * 0.8)
                ratio = display_height / image.height
                display_width = int(image.width * ratio)
            
            # Resize image
            image = image.resize((display_width, display_height), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(image)
            
            # Update label with new image
            if hasattr(self, 'flyer_label'):
                self.flyer_label.config(image=photo)
                self.flyer_label.image = photo  # Keep a reference!
                
                # Ensure the container frame fills the window
                self.flyer_container.pack(fill="both", expand=True)
                
                # Make sure the flyer label expands to show full image
                self.flyer_label.pack(fill="both", expand=True)
                
        except Exception as e:
            print(f"Error displaying flyer: {str(e)}")  # Debug logging
            self.display_error(f"Error displaying flyer: {str(e)}")

    def show_scanner_screen(self):
        """Switch to the scanner screen."""
        if self.home_frame:
            self.home_frame.pack_forget()
        
        # Create scanner frame if it doesn't exist
        if not self.scanner_frame:
            self.scanner_frame = ttk.Frame(self.root)
            
            # Get screen dimensions
            screen_width = self.root.winfo_screenwidth()
            
            # Left panel: Detected Items (70% of screen width)
            self.left_frame = ttk.Frame(
                self.scanner_frame, 
                width=int(screen_width * 0.7), 
                bootstyle="default", 
                padding=(20, 20)
            )
            self.left_frame.pack(side="left", fill="both", expand=True)
            
            # Header for scanned items
            self.items_label = ttk.Label(
                self.left_frame,
                text="Scanned Items",
                font=("Arial", 24, "bold"),
                bootstyle="default"
            )
            self.items_label.pack(anchor="w", pady=(0, 10))
            
            # Treeview for items list with frame
            list_frame = ttk.Frame(self.left_frame, bootstyle="default")
            list_frame.pack(fill="both", expand=True, pady=(0, 20))
            
            # Create and configure Treeview
            columns = ("name", "price", "quantity_controls", "action")
            self.items_list = ttk.Treeview(
                list_frame, 
                columns=columns, 
                show="headings", 
                bootstyle="info",
                height=20
            )
            
            # Configure column headings
            self.items_list.heading("name", text="Item", anchor="w")
            self.items_list.heading("price", text="Price", anchor="center")
            self.items_list.heading("quantity_controls", text="Quantity", anchor="center")
            self.items_list.heading("action", text="Action", anchor="center")
            
            # Configure column widths
            total_width = int(screen_width * 0.7) - 40
            self.items_list.column("name", width=int(total_width * 0.5), minwidth=200, anchor="w")
            self.items_list.column("price", width=int(total_width * 0.2), minwidth=100, anchor="center")
            self.items_list.column("quantity_controls", width=int(total_width * 0.15), minwidth=120, anchor="center")
            self.items_list.column("action", width=int(total_width * 0.15), minwidth=100, anchor="center")
            
            # Add scrollbar
            scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.items_list.yview)
            scrollbar.pack(side="right", fill="y")
            
            # Configure Treeview
            self.items_list.configure(yscrollcommand=scrollbar.set)
            self.items_list.pack(side="left", fill="both", expand=True)
            
            # Bind click event
            self.items_list.bind("<ButtonRelease-1>", self.on_item_click)
            
            # Style the Treeview
            style = ttk.Style()
            style.configure("Treeview", rowheight=30, font=("Arial", 12))
            style.configure("Treeview.Heading", font=("Arial", 12, "bold"))
            
            # Total amount frame
            total_frame = ttk.Frame(self.left_frame, bootstyle="default")
            total_frame.pack(fill="x", side="bottom", pady=10)
            
            # Total amount label
            self.total_label = ttk.Label(
                total_frame,
                text="Total: R0.00",
                font=("Arial", 24, "bold"),
                bootstyle="success"
            )
            self.total_label.pack(side="right", pady=10)
            
            # Right panel setup (camera and payment button)
            self.right_frame = ttk.Frame(
                self.scanner_frame, 
                width=int(screen_width * 0.3), 
                bootstyle="default", 
                padding=(20, 20)
            )
            self.right_frame.pack(side="right", fill="both")
            self.right_frame.pack_propagate(False)
            
            # Camera feed container
            camera_container = ttk.Frame(self.right_frame, bootstyle="default")
            camera_container.pack(fill="both", expand=True)
            
            self.camera_label = ttk.Label(camera_container, bootstyle="default")
            self.camera_label.pack(fill="both", expand=True)
            
            # Continue to payment button
            self.continue_button = ttk.Button(
                self.right_frame,
                text="Continue to Payment",
                command=self.show_payment_screen,
                bootstyle="success",
                padding=(20, 10)
            )
            self.continue_button.pack(side="bottom", pady=20)

        self.scanner_frame.pack(fill="both", expand=True)
        
        # Start camera
        self.cap = cv2.VideoCapture(0)
        self.running = True
        self.update_camera_feed()

    def show_payment_screen(self):
        """Switch to the payment screen."""
        self.running = False
        if self.cap:
            self.cap.release()
        self.scanner_frame.pack_forget()
        
        # Create payment frame if it doesn't exist
        if not self.payment_frame:
            self.payment_frame = ttk.Frame(self.root, bootstyle="default")
            
            # Get screen dimensions
            screen_width = self.root.winfo_screenwidth()
            
            # Left panel: Order Summary (50% of screen width)
            left_panel = ttk.Frame(
                self.payment_frame,
                width=int(screen_width * 0.5),
                bootstyle="default",
                padding=20
            )
            left_panel.pack(side="left", fill="both", expand=True)
            
            # Order summary header
            ttk.Label(
                left_panel,
                text="Order Summary",
                font=("Arial", 24, "bold"),
                bootstyle="default"
            ).pack(anchor="w", pady=(0, 20))
            
            # Create Treeview for order summary
            summary_frame = ttk.Frame(left_panel)
            summary_frame.pack(fill="both", expand=True)
            
            columns = ("name", "price", "quantity", "subtotal")
            self.summary_list = ttk.Treeview(
                summary_frame,
                columns=columns,
                show="headings",
                bootstyle="info",
                height=15
            )
            
            # Configure columns
            self.summary_list.heading("name", text="Item", anchor="w")
            self.summary_list.heading("price", text="Price", anchor="center")
            self.summary_list.heading("quantity", text="Qty", anchor="center")
            self.summary_list.heading("subtotal", text="Subtotal", anchor="center")
            
            # Set column widths
            panel_width = int(screen_width * 0.5) - 40
            self.summary_list.column("name", width=int(panel_width * 0.4), anchor="w")
            self.summary_list.column("price", width=int(panel_width * 0.2), anchor="center")
            self.summary_list.column("quantity", width=int(panel_width * 0.2), anchor="center")
            self.summary_list.column("subtotal", width=int(panel_width * 0.2), anchor="center")
            
            # Add scrollbar
            scrollbar = ttk.Scrollbar(summary_frame, orient="vertical", command=self.summary_list.yview)
            scrollbar.pack(side="right", fill="y")
            self.summary_list.configure(yscrollcommand=scrollbar.set)
            self.summary_list.pack(side="left", fill="both", expand=True)
            
            # Total amount display
            total_frame = ttk.Frame(left_panel, bootstyle="default")
            total_frame.pack(fill="x", pady=20)
            
            ttk.Label(
                total_frame,
                text=f"Total Amount: R{self.total_price:.2f}",
                font=("Arial", 20, "bold"),
                bootstyle="success"
            ).pack(side="right")
            
            # Right panel: Payment Methods (50% of screen width)
            right_panel = ttk.Frame(
                self.payment_frame,
                width=int(screen_width * 0.5),
                bootstyle="default",
                padding=20
            )
            right_panel.pack(side="right", fill="both", expand=True)
            
            # Payment methods header
            ttk.Label(
                right_panel,
                text="Select Payment Method",
                font=("Arial", 24, "bold"),
                bootstyle="default"
            ).pack(pady=(0, 30))
            
            # Payment buttons container
            payment_buttons = ttk.Frame(right_panel)
            payment_buttons.pack(fill="x", padx=50)
            
            # Payment buttons with consistent width and padding
            button_width = 25
            button_padding = (20, 15)
            
            card_payment_btn = ttk.Button(
                payment_buttons,
                text="Card Payment",
                command=lambda: self.process_payment("card"),
                bootstyle="success",
                width=button_width,
                padding=button_padding
            )
            card_payment_btn.pack(pady=10)

            contactless_btn = ttk.Button(
                payment_buttons,
                text="Contactless Payment",
                command=lambda: self.process_payment("contactless"),
                bootstyle="info",
                width=button_width,
                padding=button_padding
            )
            contactless_btn.pack(pady=10)

            cash_btn = ttk.Button(
                payment_buttons,
                text="Cash Payment",
                command=lambda: self.process_payment("cash"),
                bootstyle="warning",
                width=button_width,
                padding=button_padding
            )
            cash_btn.pack(pady=10)
            
            # QR Code display area
            self.qr_frame = ttk.Frame(right_panel, bootstyle="default")
            self.qr_frame.pack(pady=20)
            self.qr_label = ttk.Label(self.qr_frame, bootstyle="default")
            self.qr_label.pack()
            
            # Payment status label
            self.payment_status_label = ttk.Label(
                right_panel,
                text="",
                font=("Arial", 16),
                bootstyle="default"
            )
            self.payment_status_label.pack(pady=10)
            
            # Back button at bottom
            back_btn = ttk.Button(
                right_panel,
                text="Back to Scanning",
                command=self.back_to_scanning,
                bootstyle="secondary",
                width=15
            )
            back_btn.pack(side="bottom", pady=20)
            
            # Populate order summary
            self.update_order_summary()
        
        self.payment_frame.pack(fill="both", expand=True)

    def update_order_summary(self):
        """Update the order summary list."""
        # Clear existing items
        self.summary_list.delete(*self.summary_list.get_children())
        
        # Add each item to the summary
        for item in self.scanned_items:
            subtotal = item['price'] * item['quantity']
            self.summary_list.insert(
                "",
                "end",
                values=(
                    item['name'],
                    f"R{item['price']:.2f}",
                    item['quantity'],
                    f"R{subtotal:.2f}"
                )
            )

    def process_payment(self, payment_method):
        """Process the payment using PayFast integration."""
        try:
            # Clear previous QR code and status
            self.qr_label.config(image="")
            self.payment_status_label.config(text="Processing payment...")

            # Get store configuration
            with open('system_config.json', 'r') as f:
                config = json.load(f)
                store_id = config.get('store_id')
                system_id = config.get('system_id')

            # Prepare payment data for new PayFast endpoint
            payment_data = {
                "storeId": store_id,
                "systemId": system_id,
                "amount": self.total_price,
                "items": [
                    {
                        "name": item["name"],
                        "price": item["price"],
                        "quantity": item.get("quantity", 1),
                        "barcode": item.get("barcode", "")
                    }
                    for item in self.scanned_items
                ],
                "paymentMethod": payment_method
            }

            # Send payment request to new PayFast endpoint
            endpoint = f"{self.base_url}/payments/initiate"
            response = requests.post(
                endpoint,
                json=payment_data,
                headers={
                    "Authorization": f"Bearer {self.auth_token}",
                    "Content-Type": "application/json",
                    "Accept": "application/json"
                }
            )
            
            if response.status_code == 200:
                payment_info = response.json()

                if payment_info.get("success"):
                    transaction_id = payment_info.get("transactionId")
                    amount = payment_info.get("amount")

                    # Store transaction ID for status checking
                    self.current_transaction_id = transaction_id

                    # For card/contactless payments, show payment instructions
                    if payment_method in ["card", "contactless"]:
                        self.payment_status_label.config(
                            text=f"Please insert/tap your card\nAmount: R{amount}\nTransaction: {transaction_id[:8]}...",
                            bootstyle="info"
                        )

                        # Start polling for payment status
                        self.poll_payment_status(transaction_id)

                    elif payment_method == "cash":
                        # For cash payments, mark as completed immediately
                        self.payment_status_label.config(
                            text=f"Cash payment: R{amount}\nPlease collect change if applicable",
                            bootstyle="success"
                        )

                        # Simulate cash payment completion
                        self.complete_cash_payment(transaction_id)

                else:
                    error_msg = payment_info.get("message", "Payment initiation failed")
                    self.payment_status_label.config(
                        text=f"Payment failed: {error_msg}",
                        bootstyle="danger"
                    )
            else:
                error_msg = "Error generating payment QR code"
                try:
                    error_data = response.json()
                    if 'message' in error_data:
                        error_msg = error_data['message']
                except:
                    pass
                self.payment_status_label.config(
                    text=error_msg,
                    bootstyle="danger"
                )
                print(f"Payment error response: {response.text}")  # Debug logging
            
        except Exception as e:
            print(f"Payment processing error: {str(e)}")  # Add debug logging
            self.payment_status_label.config(
                text=f"Error processing payment: {str(e)}",
                bootstyle="danger"
            )

    def complete_cash_payment(self, transaction_id):
        """Complete a cash payment by updating the transaction status."""
        try:
            # For cash payments, we can mark as completed immediately
            # In a real implementation, this might involve cash drawer integration

            # Simulate a brief delay for cash handling
            self.root.after(2000, lambda: self.finalize_cash_payment(transaction_id))

        except Exception as e:
            self.payment_status_label.config(
                text=f"Error processing cash payment: {str(e)}",
                bootstyle="danger"
            )

    def finalize_cash_payment(self, transaction_id):
        """Finalize the cash payment and generate receipt."""
        try:
            # Update payment status to completed
            # In production, you might call a specific endpoint to mark cash payment as complete
            self.payment_status_label.config(
                text="Cash payment completed! Generating receipt...",
                bootstyle="success"
            )

            # Generate receipt
            self.generate_receipt()

        except Exception as e:
            self.payment_status_label.config(
                text=f"Error finalizing cash payment: {str(e)}",
                bootstyle="danger"
            )

    def poll_payment_status(self, transaction_id):
        """Poll the backend for payment status updates."""
        try:
            response = requests.get(
                f"{self.base_url}/payments/status/{transaction_id}",
                headers={"Authorization": f"Bearer {self.auth_token}"}
            )
            
            if response.status_code == 200:
                status = response.json().get("status")
                
                if status == "completed":
                    self.payment_status_label.config(
                        text="Payment successful! Generating receipt...",
                        bootstyle="success"
                    )
                    self.generate_receipt()
                elif status == "failed":
                    self.payment_status_label.config(
                        text="Payment failed. Please try again.",
                        bootstyle="danger"
                    )
                else:
                    # Continue polling if payment is still pending
                    self.root.after(2000, lambda: self.poll_payment_status(transaction_id))
                
        except Exception as e:
            self.payment_status_label.config(
                text=f"Error checking payment status: {str(e)}",
                bootstyle="danger"
            )

    def generate_receipt(self):
        """Generate, display, and print receipt after successful payment."""
        try:
            response = requests.post(
                f"{self.base_url}/products/receipt",
                json={
                    "products": self.scanned_items,
                    "totalAmount": self.total_price
                }
            )
            
            if response.status_code == 200:
                # Hide payment screen
                self.payment_frame.pack_forget()
                
                # Create and show receipt screen
                self.show_receipt_screen(response.json().get("receiptPath"))
            else:
                messagebox.showerror(
                    "Error",
                    "Failed to generate receipt. Please contact support."
                )
            
        except Exception as e:
            messagebox.showerror("Error", f"Error generating receipt: {str(e)}")

    def show_receipt_screen(self, receipt_path):
        """Display the receipt and thank you message."""
        # Create receipt frame
        self.receipt_frame = ttk.Frame(self.root, bootstyle="default")
        
        # Receipt header
        ttk.Label(
            self.receipt_frame,
            text="Your Receipt",
            font=("Arial", 24, "bold"),
            bootstyle="default"
        ).pack(pady=20)
        
        # Receipt content
        receipt_text = ttk.Text(
            self.receipt_frame,
            height=20,
            width=40,
            font=("Courier", 12),
            bootstyle="default",
            relief="flat"
        )
        receipt_text.pack(pady=20)
        
        # Add receipt content
        receipt_text.insert("1.0", "\n=== PURCHASE RECEIPT ===\n\n")
        receipt_text.insert("end", f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        receipt_text.insert("end", "Items:\n")
        receipt_text.insert("end", "-" * 40 + "\n")
        
        for item in self.scanned_items:
            receipt_text.insert("end", f"{item['name']}\n")
            receipt_text.insert("end", f"R{item['price']:.2f}\n")
            receipt_text.insert("end", "-" * 40 + "\n")
        
        receipt_text.insert("end", f"\nTotal Amount: R{self.total_price:.2f}\n")
        receipt_text.insert("end", "\nThank you for shopping with us!")
        receipt_text.config(state="disabled")
        
        # Print receipt button
        print_btn = ttk.Button(
            self.receipt_frame,
            text="Print Receipt",
            command=lambda: self.print_receipt(receipt_path),
            bootstyle="primary",
            width=15
        )
        print_btn.pack(pady=10)
        
        # Thank you message
        ttk.Label(
            self.receipt_frame,
            text="Thank You For Shopping With Us!",
            font=("Arial", 28, "bold"),
            bootstyle="success"
        ).pack(pady=20)
        
        # Timer label
        self.timer_label = ttk.Label(
            self.receipt_frame,
            text="Returning to home screen in 10 seconds...",
            font=("Arial", 14),
            bootstyle="default"
        )
        self.timer_label.pack(pady=10)
        
        self.receipt_frame.pack(fill="both", expand=True)
        
        # Start countdown to return to home
        self.countdown_to_home(10)

    def countdown_to_home(self, seconds):
        """Countdown timer to return to home screen."""
        if seconds > 0:
            self.timer_label.config(text=f"Returning to home screen in {seconds} seconds...")
            self.root.after(1000, lambda: self.countdown_to_home(seconds - 1))
        else:
            self.receipt_frame.destroy()
            self.reset_and_return_home()

    def print_receipt(self, receipt_path):
        """Print the receipt."""
        try:
            # Get the default printer
            printer_name = win32print.GetDefaultPrinter()
            
            # Open the receipt file
            if os.path.exists(receipt_path):
                # Print the file
                with open(receipt_path, 'rb') as file:
                    raw_data = file.read()
                    
                hprinter = win32print.OpenPrinter(printer_name)
                try:
                    win32print.StartDocPrinter(hprinter, 1, ("Receipt", None, "RAW"))
                    try:
                        win32print.StartPagePrinter(hprinter)
                        win32print.WritePrinter(hprinter, raw_data)
                        win32print.EndPagePrinter(hprinter)
                    finally:
                        win32print.EndDocPrinter(hprinter)
                finally:
                    win32print.ClosePrinter(hprinter)
                    
                messagebox.showinfo("Success", "Receipt printed successfully!")
            else:
                messagebox.showerror("Error", "Receipt file not found")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to print receipt: {str(e)}")

    def back_to_scanning(self):
        """Return to the scanning screen."""
        self.payment_frame.pack_forget()
        self.show_scanner_screen()

    def reset_and_return_home(self):
        """Reset the application state and return to home screen."""
        # Clear all data
        self.detected_barcodes.clear()
        self.total_price = 0
        self.scanned_items.clear()
        
        # Remove all frames
        if hasattr(self, 'payment_frame'):
            self.payment_frame.pack_forget()
        if hasattr(self, 'receipt_frame'):
            self.receipt_frame.pack_forget()
        
        # Show home screen
        self.show_home_screen()

    def update_camera_feed(self):
        """Update the camera feed and detect barcodes."""
        if not self.running:
            return

        ret, frame = self.cap.read()
        if ret:
            # Convert to grayscale for better barcode detection
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Detect barcodes
            barcodes = self.detect_barcodes(gray)

            for barcode in barcodes:
                barcode_data = barcode.data.decode("utf-8")
                if barcode_data not in self.detected_barcodes:
                    self.detected_barcodes.add(barcode_data)
                    self.fetch_product_data(barcode_data)

            # Convert frame for display
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            img = Image.fromarray(frame_rgb)
            img_tk = ImageTk.PhotoImage(img)
            self.camera_label.imgtk = img_tk
            self.camera_label.configure(image=img_tk)

        self.root.after(10, self.update_camera_feed)

    def detect_barcodes(self, frame):
        """Detect barcodes in the given frame."""
        barcodes = []
        try:
            # Detect both QR codes and standard barcodes
            detected = pyzbar.decode(frame)
            
            for barcode in detected:
                try:
                    # Extract barcode data
                    barcode_data = barcode.data.decode("utf-8")
                    barcode_type = barcode.type
                    
                    # Validate barcode data
                    if not barcode_data or len(barcode_data.strip()) == 0:
                        continue
                    
                    # Draw rectangle around the barcode
                    (x, y, w, h) = barcode.rect
                    cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
                    
                    # Put barcode type and data on the image
                    text = f"{barcode_type}: {barcode_data}"
                    cv2.putText(frame, text, (x, y - 10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                    
                    barcodes.append(barcode)
                    
                    # Add debug logging
                    print(f"Detected {barcode_type}: {barcode_data}")
                    
                except UnicodeDecodeError:
                    print("Error decoding barcode data")
                    continue
                
            return barcodes
                
        except Exception as e:
            print("Error detecting barcodes:", str(e))
            return []

    def fetch_product_data(self, barcode):
        """Send barcode to the backend and fetch product data."""
        try:
            # Validate barcode data
            if not barcode or len(str(barcode).strip()) == 0:
                print("Invalid barcode data")
                return

            # Check if item already exists in scanned items
            for item in self.scanned_items:
                if item['barcode'] == str(barcode).strip():
                    # Increment quantity and update display
                    item['quantity'] += 1
                    self.update_items_display()
                    return

            # Debug print
            print(f"Sending barcode to server: {barcode}")
            
            response = requests.post(
                f"{self.base_url}/products/scan",
                json={"barcode": str(barcode).strip()},
                headers={
                    "Authorization": f"Bearer {self.auth_token}",
                    "Content-Type": "application/json",
                    "Accept": "application/json"
                }
            )

            # Debug print
            print(f"Response status code: {response.status_code}")
            print(f"Response headers: {response.headers}")
            
            if response.status_code == 200:
                product = response.json()
                # Add barcode to product data
                product['barcode'] = str(barcode).strip()
                print(f"Received product data: {product}")  # Debug print
                self.add_product_to_list(product)
            else:
                print(f"Error fetching product data: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"Error details: {error_data}")
                except:
                    print(f"Raw response: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"Network error: {str(e)}")
        except Exception as e:
            print(f"Error connecting to the server: {str(e)}")

    def add_product_to_list(self, product):
        """Add the product to the list and update the total."""
        try:
            # Check if product already exists
            for item in self.scanned_items:
                if item['barcode'] == product['barcode']:
                    item['quantity'] += 1
                    print(f"Updated quantity for {item['name']}")
                    self.update_items_display()
                    return

            # Add new product with quantity
            product['quantity'] = 1
            self.scanned_items.append(product)
            print(f"Added new product: {product['name']}")
            
            # Update display
            self.update_items_display()
            
        except Exception as e:
            print(f"Error adding product to list: {str(e)}")
            self.display_error(f"Error adding product: {str(e)}")

    def update_items_display(self):
        """Update the Treeview with scanned items."""
        try:
            # Clear existing items
            self.items_list.delete(*self.items_list.get_children())
            
            # Add each item to the Treeview
            for item in self.scanned_items:
                item_id = item['barcode']
                
                # Create quantity control frame
                qty_frame = ttk.Frame(self.items_list)
                
                # Decrease button
                dec_btn = ttk.Button(
                    qty_frame,
                    text="-",
                    bootstyle="danger-outline",
                    width=2,
                    command=lambda i=item: self.update_quantity(i, -1)
                )
                dec_btn.pack(side="left", padx=2)
                
                # Quantity label
                qty_label = ttk.Label(
                    qty_frame,
                    text=str(item['quantity']),
                    width=3
                )
                qty_label.pack(side="left", padx=2)
                
                # Increase button
                inc_btn = ttk.Button(
                    qty_frame,
                    text="+",
                    bootstyle="success-outline",
                    width=2,
                    command=lambda i=item: self.update_quantity(i, 1)
                )
                inc_btn.pack(side="left", padx=2)
                
                # Delete button
                del_frame = ttk.Frame(self.items_list)
                del_btn = ttk.Button(
                    del_frame,
                    text="Delete",
                    bootstyle="danger",
                    command=lambda i=item: self.confirm_delete(i)
                )
                del_btn.pack(fill="x")
                
                # Insert item into Treeview
                self.items_list.insert(
                    "", 
                    "end",
                    iid=item_id,
                    values=(
                        item['name'],
                        f"R{item['price']:.2f}",
                        "",  # Empty string for quantity column
                        ""   # Empty string for action column
                    )
                )
                
                # Place the frames in the Treeview
                self.items_list.window(item_id, "quantity_controls", qty_frame)
                self.items_list.window(item_id, "action", del_frame)
            
            # Update the total
            self.update_total()
            
        except Exception as e:
            print(f"Error updating items display: {str(e)}")
            self.display_error(f"Error updating display: {str(e)}")

    def update_quantity(self, item, change):
        """Update the quantity of an item."""
        new_quantity = item['quantity'] + change
        
        if new_quantity > 0:
            item['quantity'] = new_quantity
        elif new_quantity == 0:
            self.remove_item(item)
            return
        
        self.update_items_display()

    def remove_item(self, item):
        """Remove an item from the list."""
        self.scanned_items.remove(item)
        self.update_items_display()

    def update_total(self):
        """Update the total price display."""
        self.total_price = sum(item['price'] * item['quantity'] for item in self.scanned_items)
        self.total_label.config(text=f"Total: R{self.total_price:.2f}")

    def on_item_click(self, event):
        """Handle item click in Treeview."""
        selected_item = self.items_list.selection()
        if not selected_item:
            return
        
        item_id = selected_item[0]
        for item in self.scanned_items:
            if item["barcode"] == item_id:
                self.modify_quantity_popup(item)
                break

    def modify_quantity_popup(self, item):
        """Show popup for modifying item quantity."""
        popup = tk.Toplevel(self.root)
        popup.title("Modify Quantity")
        popup.geometry("300x150")
        
        ttk.Label(
            popup, 
            text=f"Modify {item['name']}", 
            font=("Arial", 14)
        ).pack(pady=10)
        
        qty_var = tk.IntVar(value=item["quantity"])
        qty_entry = ttk.Entry(popup, textvariable=qty_var, width=5)
        qty_entry.pack()
        
        def update_qty():
            try:
                new_qty = int(qty_var.get())
                if new_qty > 0:
                    item["quantity"] = new_qty
                    self.update_items_display()
                else:
                    self.scanned_items.remove(item)
                    self.update_items_display()
                popup.destroy()
            except ValueError:
                messagebox.showerror("Error", "Enter a valid quantity.")
        
        ttk.Button(
            popup, 
            text="Update", 
            command=update_qty, 
            bootstyle="primary"
        ).pack(pady=10)

    def confirm_delete(self, item):
        """Show confirmation dialog before deleting item."""
        result = messagebox.askyesno(
            "Confirm Delete",
            f"Are you sure you want to remove {item['name']}?",
            icon='warning'
        )
        if result:
            self.remove_item(item)

    def stop(self):
        """Stop the application."""
        self.running = False
        if self.cap:
            self.cap.release()
        cv2.destroyAllWindows()
        self.root.quit()

# Main Application
if __name__ == "__main__":
    app = SelfCheckoutSystem(None)  # Pass None since we create the window in __init__
    app.root.protocol("WM_DELETE_WINDOW", app.stop)
    app.root.mainloop()
