# Complete Self-Checkout System Installation Guide

## 📋 System Overview

This installation guide covers the complete setup of your multi-vendor self-checkout system:

- **Backend API** (`/backend/`) - Node.js + Express + MongoDB
- **Web Frontend** (`/ui/`) - React + Vite for admin/store management
- **Desktop App** (`sys2.py`) - Python Tkinter for Raspberry Pi trolleys
- **PayFast POS Integration** - Card payment processing

## 🛠️ Prerequisites

### Hardware Requirements

#### **Server/Development Machine**
- CPU: 2+ cores, 4GB+ RAM
- Storage: 20GB+ available space
- OS: Windows 10/11, macOS, or Linux
- Network: Stable internet connection

#### **Raspberry Pi Trolley Setup (Per Trolley)**
- Raspberry Pi 4 (4GB+ RAM recommended)
- 32GB+ microSD card (Class 10)
- 7" Touchscreen display
- USB Camera (1080p recommended)
- PayFast POS card terminal
- Thermal receipt printer
- Power supply (5V 3A)
- WiFi connectivity

### Software Prerequisites

#### **Development/Server Machine**
- Node.js 18+ and npm
- MongoDB 6.0+
- Git
- Code editor (VS Code recommended)

#### **Raspberry Pi**
- Raspberry Pi OS (64-bit recommended)
- Python 3.9+
- Camera and GPIO access enabled

## 🚀 Installation Steps

### Phase 1: Backend API Setup

#### 1.1 Clone and Setup Backend
```bash
# Clone the repository
git clone <your-repo-url>
cd checkout/backend

# Install dependencies
npm install

# Create environment file
cp .env.example .env
```

#### 1.2 Configure Environment Variables
Edit `.env` file:
```env
# Database
MONGODB_URI=mongodb://localhost:27017/selfcheckout
# or MongoDB Atlas: mongodb+srv://username:<EMAIL>/selfcheckout

# Security
JWT_SECRET=your_super_secure_jwt_secret_here_min_32_chars

# Server
PORT=5000
NODE_ENV=production
BASE_URL=http://localhost:5000

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# PayFast (will be configured per store)
PAYFAST_SANDBOX=true
```

#### 1.3 Setup MongoDB

**Option A: Local MongoDB**
```bash
# Install MongoDB Community Edition
# Windows: Download from mongodb.com
# macOS: brew install mongodb-community
# Ubuntu: sudo apt install mongodb

# Start MongoDB service
# Windows: Start MongoDB service from Services
# macOS/Linux: sudo systemctl start mongod
```

**Option B: MongoDB Atlas (Cloud)**
1. Create account at [mongodb.com/atlas](https://mongodb.com/atlas)
2. Create free cluster
3. Get connection string
4. Update `MONGODB_URI` in `.env`

#### 1.4 Initialize Database
```bash
# Seed initial data (admin user, sample store)
npm run seed

# Start backend server
npm start
```

Verify backend is running: `http://localhost:5000/api/stores`

### Phase 2: Web Frontend Setup

#### 2.1 Setup Frontend
```bash
cd ../ui

# Install dependencies
npm install

# Create environment file
echo "VITE_API_URL=http://localhost:5000/api" > .env.local
```

#### 2.2 Start Development Server
```bash
# Start frontend development server
npm run dev
```

Access web interface: `http://localhost:5173`

**Default Admin Login:**
- Email: `<EMAIL>`
- Password: `admin123`

### Phase 3: Raspberry Pi Setup

#### 3.1 Prepare Raspberry Pi OS
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python dependencies
sudo apt install python3-pip python3-venv -y

# Install system libraries for camera and GUI
sudo apt install python3-opencv python3-tk -y
sudo apt install libzbar0 libzbar-dev -y

# Enable camera
sudo raspi-config
# Navigate to Interface Options > Camera > Enable
```

#### 3.2 Install Python Dependencies
```bash
# Create virtual environment
python3 -m venv selfcheckout_env
source selfcheckout_env/bin/activate

# Install required packages
pip install opencv-python
pip install pyzbar
pip install ttkbootstrap
pip install requests
pip install pillow
pip install pywin32  # For Windows only, skip on Pi
```

#### 3.3 Copy Desktop Application
```bash
# Copy sys2.py to Raspberry Pi
scp sys2.py pi@<pi-ip-address>:/home/<USER>/
scp system_config.json pi@<pi-ip-address>:/home/<USER>/
```

#### 3.4 Configure System
Edit `system_config.json` on each Pi:
```json
{
    "system_id": "STORE1-TROLLEY-001",
    "store_name": "Your Store Name",
    "store_id": "your_mongodb_store_id",
    "backend_url": "http://your-server-ip:5000/api"
}
```

### Phase 4: PayFast Integration Setup

#### 4.1 PayFast Account Setup (Per Store)
1. **Register Store Account**
   - Visit [payfast.io](https://payfast.io)
   - Complete business verification
   - Provide banking details

2. **Order POS Device**
   - Digital Card Machine: R999
   - Printer Card Machine: R1,499
   - Monthly connectivity: R49

3. **Get Credentials**
   - Login to PayFast dashboard
   - Navigate to Settings > Integration
   - Note: Merchant ID, Merchant Key
   - Set Passphrase in Settings

#### 4.2 Configure PayFast in System
```bash
# Login to web interface as admin
# Navigate to Stores > Select Store > PayFast Settings

# Enter PayFast credentials:
# - Merchant ID: from PayFast dashboard
# - Merchant Key: from PayFast dashboard  
# - Passphrase: set in PayFast settings
# - Sandbox: false (for production)
# - POS Device ID: from physical device

# Test connection and activate
```

### Phase 5: Hardware Integration

#### 5.1 Connect Hardware Components

**Touchscreen Setup:**
```bash
# Enable touchscreen in config
sudo nano /boot/config.txt
# Add: dtoverlay=vc4-kms-v3d
# Add: dtoverlay=vc4-fkms-v3d

# Calibrate touchscreen
sudo apt install xinput-calibrator
xinput_calibrator
```

**Camera Setup:**
```bash
# Test camera
raspistill -o test.jpg
# Verify image is captured

# Test in Python
python3 -c "import cv2; cap = cv2.VideoCapture(0); print('Camera OK' if cap.isOpened() else 'Camera Error')"
```

**Printer Setup:**
```bash
# Install CUPS for printer management
sudo apt install cups cups-client -y

# Add printer (thermal receipt printer)
sudo lpadmin -p ReceiptPrinter -E -v usb://your-printer-uri -m raw
sudo lpoptions -d ReceiptPrinter
```

**POS Terminal Setup:**
```bash
# Connect PayFast terminal via USB/Bluetooth
# Terminal will be configured through PayFast dashboard
# Test connection with PayFast support
```

#### 5.2 Auto-Start Configuration
```bash
# Create systemd service for auto-start
sudo nano /etc/systemd/system/selfcheckout.service
```

Add service configuration:
```ini
[Unit]
Description=Self Checkout System
After=network.target

[Service]
Type=simple
User=pi
WorkingDirectory=/home/<USER>
Environment=DISPLAY=:0
ExecStart=/home/<USER>/selfcheckout_env/bin/python /home/<USER>/sys2.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Enable auto-start:
```bash
sudo systemctl enable selfcheckout.service
sudo systemctl start selfcheckout.service
```

### Phase 6: Production Deployment

#### 6.1 Secure Backend Deployment

**Using PM2 (Recommended):**
```bash
# Install PM2
npm install -g pm2

# Start backend with PM2
cd backend
pm2 start server.js --name "selfcheckout-api"
pm2 startup
pm2 save
```

**Using Docker (Alternative):**
```bash
# Create Dockerfile in backend/
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 5000
CMD ["npm", "start"]

# Build and run
docker build -t selfcheckout-backend .
docker run -d -p 5000:5000 --name selfcheckout-api selfcheckout-backend
```

#### 6.2 Frontend Production Build
```bash
cd ui

# Build for production
npm run build

# Serve with nginx or Apache
# Copy dist/ folder to web server
```

#### 6.3 Database Backup Setup
```bash
# Create backup script
#!/bin/bash
mongodump --uri="$MONGODB_URI" --out="/backup/$(date +%Y%m%d_%H%M%S)"

# Add to crontab for daily backups
crontab -e
# Add: 0 2 * * * /path/to/backup-script.sh
```

### Phase 7: Testing and Validation

#### 7.1 System Integration Test
```bash
# Run automated tests
cd backend
npm test

# Run PayFast integration test
node test-payfast-integration.js
```

#### 7.2 End-to-End Testing
1. **Web Interface Test**
   - Login as admin
   - Create/configure store
   - Add products
   - Configure PayFast
   - Generate reports

2. **Desktop App Test**
   - Start sys2.py on Pi
   - Test barcode scanning
   - Test payment flow
   - Test receipt printing

3. **Payment Integration Test**
   - Process test transaction
   - Verify PayFast webhook
   - Check transaction in database
   - Confirm receipt generation

## 🔧 Configuration Files

### Backend Environment (.env)
```env
MONGODB_URI=mongodb://localhost:27017/selfcheckout
JWT_SECRET=your_jwt_secret_min_32_characters_long
PORT=5000
NODE_ENV=production
BASE_URL=https://your-domain.com
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads
PAYFAST_SANDBOX=false
```

### Frontend Environment (.env.local)
```env
VITE_API_URL=https://your-api-domain.com/api
```

### System Configuration (system_config.json)
```json
{
    "system_id": "STORE1-TROLLEY-001",
    "store_name": "Main Store",
    "store_id": "507f1f77bcf86cd799439011",
    "backend_url": "https://your-api-domain.com/api"
}
```

## 🚨 Troubleshooting

### Common Issues

#### Backend Won't Start
```bash
# Check MongoDB connection
mongo --eval "db.adminCommand('ismaster')"

# Check port availability
netstat -tulpn | grep :5000

# Check logs
npm start 2>&1 | tee backend.log
```

#### Frontend Build Fails
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Check Node.js version
node --version  # Should be 18+
```

#### Raspberry Pi Camera Issues
```bash
# Check camera module
vcgencmd get_camera

# Test camera access
sudo usermod -a -G video pi
# Reboot required after group change
```

#### PayFast Integration Issues
```bash
# Test PayFast credentials
curl -X POST http://localhost:5000/api/stores/STORE_ID/payfast/test \
  -H "Authorization: Bearer YOUR_TOKEN"

# Check webhook delivery in PayFast dashboard
# Verify signature generation matches PayFast requirements
```

## 📞 Support

### Documentation
- [PayFast Developer Docs](https://developers.payfast.co.za/)
- [MongoDB Documentation](https://docs.mongodb.com/)
- [React Documentation](https://react.dev/)

### System Logs
- Backend: `backend/error.log`
- Frontend: Browser developer console
- Desktop App: Terminal output
- System: `journalctl -u selfcheckout.service`

### Monitoring
- API Health: `GET /api/health`
- Database Status: MongoDB Compass
- Payment Status: PayFast Dashboard
- System Status: `systemctl status selfcheckout`

## 📦 Quick Installation Scripts

### Automated Backend Setup Script
```bash
#!/bin/bash
# save as: install-backend.sh

echo "🚀 Installing Self-Checkout Backend..."

# Install Node.js if not present
if ! command -v node &> /dev/null; then
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Install MongoDB if not present
if ! command -v mongod &> /dev/null; then
    wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
    echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
    sudo apt-get update
    sudo apt-get install -y mongodb-org
    sudo systemctl start mongod
    sudo systemctl enable mongod
fi

# Setup backend
cd backend
npm install
cp .env.example .env

echo "✅ Backend setup complete!"
echo "📝 Please edit .env file with your configuration"
echo "🚀 Run 'npm start' to start the server"
```

### Automated Raspberry Pi Setup Script
```bash
#!/bin/bash
# save as: install-pi.sh

echo "🍓 Setting up Raspberry Pi for Self-Checkout..."

# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install -y python3-pip python3-venv python3-opencv python3-tk
sudo apt install -y libzbar0 libzbar-dev

# Enable camera
sudo raspi-config nonint do_camera 0

# Create virtual environment
python3 -m venv selfcheckout_env
source selfcheckout_env/bin/activate

# Install Python packages
pip install opencv-python pyzbar ttkbootstrap requests pillow

# Setup auto-start service
sudo tee /etc/systemd/system/selfcheckout.service > /dev/null <<EOF
[Unit]
Description=Self Checkout System
After=network.target

[Service]
Type=simple
User=pi
WorkingDirectory=/home/<USER>
Environment=DISPLAY=:0
ExecStart=/home/<USER>/selfcheckout_env/bin/python /home/<USER>/sys2.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl enable selfcheckout.service

echo "✅ Raspberry Pi setup complete!"
echo "📝 Copy sys2.py and system_config.json to /home/<USER>/"
echo "🚀 Run 'sudo systemctl start selfcheckout' to start"
```

### Docker Compose Setup
```yaml
# save as: docker-compose.yml
version: '3.8'

services:
  mongodb:
    image: mongo:6.0
    container_name: selfcheckout-db
    restart: always
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123

  backend:
    build: ./backend
    container_name: selfcheckout-api
    restart: always
    ports:
      - "5000:5000"
    depends_on:
      - mongodb
    environment:
      MONGODB_URI: ***********************************************************************
      JWT_SECRET: your_jwt_secret_here
      NODE_ENV: production
    volumes:
      - ./backend/uploads:/app/uploads

  frontend:
    build: ./ui
    container_name: selfcheckout-web
    restart: always
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  mongodb_data:
```

---

**🎉 Congratulations! Your self-checkout system is now fully installed and ready for production use!**

## 🔗 Quick Links
- [PayFast Setup Guide](docs/payfast-setup-guide.md)
- [Architecture Documentation](docs/payfast-integration-architecture.md)
- [API Documentation](http://localhost:5000/api-docs) (when running)
- [PayFast Developer Portal](https://developers.payfast.co.za/)
