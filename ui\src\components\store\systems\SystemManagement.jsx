import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  LinearProgress,
  Avatar,
  Tooltip,
  Menu,
  MenuItem,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Computer,
  PowerSettingsNew,
  Refresh,
  Settings,
  Warning,
  CheckCircle,
  Error,
  Camera,
  Print,
  CreditCard,
  Wifi,
  Battery,
  Memory,
  Storage,
  MoreVert,
  Add,
  Edit,
  Delete,
  RemoteControl,
  Visibility,
  RestartAlt
} from '@mui/icons-material';

const SystemManagement = ({ storeId }) => {
  const [systems, setSystems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedSystem, setSelectedSystem] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [actionMenuAnchor, setActionMenuAnchor] = useState(null);
  const [actionMenuSystem, setActionMenuSystem] = useState(null);

  useEffect(() => {
    fetchSystems();
    const interval = setInterval(fetchSystems, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, [storeId]);

  const fetchSystems = async () => {
    try {
      const response = await fetch(`/api/systems/store/${storeId}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      setSystems(data.systems || []);
    } catch (error) {
      console.error('Error fetching systems:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Active': return 'success';
      case 'Inactive': return 'error';
      case 'Maintenance': return 'warning';
      default: return 'default';
    }
  };

  const getHealthColor = (health) => {
    if (health >= 90) return 'success';
    if (health >= 70) return 'warning';
    return 'error';
  };

  const SystemCard = ({ system }) => {
    const mockHealth = {
      overall: Math.floor(Math.random() * 30) + 70, // 70-100%
      cpu: Math.floor(Math.random() * 40) + 20, // 20-60%
      memory: Math.floor(Math.random() * 50) + 30, // 30-80%
      storage: Math.floor(Math.random() * 60) + 20, // 20-80%
      network: Math.random() > 0.1 ? 'Connected' : 'Disconnected',
      camera: Math.random() > 0.05 ? 'Working' : 'Error',
      printer: Math.random() > 0.1 ? 'Ready' : 'Paper Low',
      cardReader: Math.random() > 0.05 ? 'Connected' : 'Error'
    };

    return (
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
            <Box display="flex" alignItems="center">
              <Avatar
                sx={{
                  bgcolor: getStatusColor(system.status) + '.main',
                  mr: 2
                }}
              >
                <Computer />
              </Avatar>
              <Box>
                <Typography variant="h6">{system.name || system.location}</Typography>
                <Typography variant="body2" color="textSecondary">
                  {system.systemId}
                </Typography>
              </Box>
            </Box>
            <Box display="flex" alignItems="center" gap={1}>
              <Chip
                label={system.status}
                color={getStatusColor(system.status)}
                size="small"
              />
              <IconButton
                size="small"
                onClick={(e) => {
                  setActionMenuAnchor(e.currentTarget);
                  setActionMenuSystem(system);
                }}
              >
                <MoreVert />
              </IconButton>
            </Box>
          </Box>

          {/* System Health */}
          <Box mb={2}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
              <Typography variant="body2" color="textSecondary">
                System Health
              </Typography>
              <Typography variant="body2" fontWeight="bold" color={getHealthColor(mockHealth.overall) + '.main'}>
                {mockHealth.overall}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={mockHealth.overall}
              color={getHealthColor(mockHealth.overall)}
              sx={{ height: 6, borderRadius: 3 }}
            />
          </Box>

          {/* Resource Usage */}
          <Grid container spacing={1} mb={2}>
            <Grid item xs={6}>
              <Box>
                <Typography variant="caption" color="textSecondary">
                  CPU
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={mockHealth.cpu}
                  size="small"
                  sx={{ height: 4 }}
                />
                <Typography variant="caption">
                  {mockHealth.cpu}%
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6}>
              <Box>
                <Typography variant="caption" color="textSecondary">
                  Memory
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={mockHealth.memory}
                  size="small"
                  sx={{ height: 4 }}
                />
                <Typography variant="caption">
                  {mockHealth.memory}%
                </Typography>
              </Box>
            </Grid>
          </Grid>

          {/* Hardware Status */}
          <Box display="flex" justifyContent="space-between" mb={2}>
            <Tooltip title={`Network: ${mockHealth.network}`}>
              <Wifi color={mockHealth.network === 'Connected' ? 'success' : 'error'} />
            </Tooltip>
            <Tooltip title={`Camera: ${mockHealth.camera}`}>
              <Camera color={mockHealth.camera === 'Working' ? 'success' : 'error'} />
            </Tooltip>
            <Tooltip title={`Printer: ${mockHealth.printer}`}>
              <Print color={mockHealth.printer === 'Ready' ? 'success' : 'warning'} />
            </Tooltip>
            <Tooltip title={`Card Reader: ${mockHealth.cardReader}`}>
              <CreditCard color={mockHealth.cardReader === 'Connected' ? 'success' : 'error'} />
            </Tooltip>
          </Box>

          {/* Last Seen */}
          <Typography variant="caption" color="textSecondary">
            Last seen: {system.lastSeen ? new Date(system.lastSeen).toLocaleString() : 'Never'}
          </Typography>
        </CardContent>
      </Card>
    );
  };

  const SystemActionMenu = () => (
    <Menu
      anchorEl={actionMenuAnchor}
      open={Boolean(actionMenuAnchor)}
      onClose={() => setActionMenuAnchor(null)}
    >
      <MenuItem onClick={() => handleSystemAction('view')}>
        <Visibility sx={{ mr: 1 }} />
        View Details
      </MenuItem>
      <MenuItem onClick={() => handleSystemAction('restart')}>
        <RestartAlt sx={{ mr: 1 }} />
        Restart System
      </MenuItem>
      <MenuItem onClick={() => handleSystemAction('remote')}>
        <RemoteControl sx={{ mr: 1 }} />
        Remote Control
      </MenuItem>
      <MenuItem onClick={() => handleSystemAction('settings')}>
        <Settings sx={{ mr: 1 }} />
        Settings
      </MenuItem>
      <MenuItem onClick={() => handleSystemAction('maintenance')}>
        <Warning sx={{ mr: 1 }} />
        Maintenance Mode
      </MenuItem>
    </Menu>
  );

  const handleSystemAction = (action) => {
    console.log(`${action} for system:`, actionMenuSystem);
    setActionMenuAnchor(null);
    
    switch (action) {
      case 'view':
        setSelectedSystem(actionMenuSystem);
        setDialogOpen(true);
        break;
      case 'restart':
        // Handle system restart
        break;
      case 'remote':
        // Handle remote control
        break;
      case 'settings':
        // Handle settings
        break;
      case 'maintenance':
        // Handle maintenance mode
        break;
    }
  };

  const SystemDetailsDialog = () => (
    <Dialog
      open={dialogOpen}
      onClose={() => setDialogOpen(false)}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        System Details - {selectedSystem?.name || selectedSystem?.location}
      </DialogTitle>
      <DialogContent>
        {selectedSystem && (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                System Information
              </Typography>
              <Table size="small">
                <TableBody>
                  <TableRow>
                    <TableCell>System ID</TableCell>
                    <TableCell>{selectedSystem.systemId}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Location</TableCell>
                    <TableCell>{selectedSystem.location}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Status</TableCell>
                    <TableCell>
                      <Chip
                        label={selectedSystem.status}
                        color={getStatusColor(selectedSystem.status)}
                        size="small"
                      />
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Registered</TableCell>
                    <TableCell>
                      {new Date(selectedSystem.registeredAt).toLocaleDateString()}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Hardware Status
              </Typography>
              <Box display="flex" flexDirection="column" gap={2}>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="body2">Camera</Typography>
                  <Chip label="Working" color="success" size="small" />
                </Box>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="body2">Printer</Typography>
                  <Chip label="Ready" color="success" size="small" />
                </Box>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="body2">Card Reader</Typography>
                  <Chip label="Connected" color="success" size="small" />
                </Box>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="body2">Network</Typography>
                  <Chip label="Connected" color="success" size="small" />
                </Box>
              </Box>
            </Grid>
          </Grid>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setDialogOpen(false)}>Close</Button>
        <Button variant="contained" onClick={() => {/* Handle save */}}>
          Save Changes
        </Button>
      </DialogActions>
    </Dialog>
  );

  if (loading) {
    return <LinearProgress />;
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">System Management</Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<Add />}
            onClick={() => {/* Handle add system */}}
          >
            Add System
          </Button>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={fetchSystems}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {/* System Overview */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Systems
              </Typography>
              <Typography variant="h4" color="primary">
                {systems.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Active Systems
              </Typography>
              <Typography variant="h4" color="success.main">
                {systems.filter(s => s.status === 'Active').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Maintenance
              </Typography>
              <Typography variant="h4" color="warning.main">
                {systems.filter(s => s.status === 'Maintenance').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Offline
              </Typography>
              <Typography variant="h4" color="error.main">
                {systems.filter(s => s.status === 'Inactive').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Systems Grid */}
      <Grid container spacing={3}>
        {systems.map((system) => (
          <Grid item xs={12} md={6} lg={4} key={system.systemId}>
            <SystemCard system={system} />
          </Grid>
        ))}
      </Grid>

      {systems.length === 0 && (
        <Card>
          <CardContent>
            <Box textAlign="center" py={4}>
              <Computer sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="textSecondary" gutterBottom>
                No Systems Registered
              </Typography>
              <Typography variant="body2" color="textSecondary" mb={3}>
                Register your first self-checkout system to start monitoring.
              </Typography>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={() => {/* Handle add system */}}
              >
                Add First System
              </Button>
            </Box>
          </CardContent>
        </Card>
      )}

      <SystemActionMenu />
      <SystemDetailsDialog />
    </Box>
  );
};

export default SystemManagement;
