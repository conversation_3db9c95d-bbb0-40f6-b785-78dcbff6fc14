const express = require("express");
const { authenticate, authorize } = require("../middleware/authMiddleware");
const {
  getStoreFinancials,
  generateTaxReport,
  getProfitAnalysis
} = require("../controllers/financialController");

const router = express.Router();

// Store financial data
router.get("/store/:storeId", authenticate, authorize(["admin", "store"]), getStoreFinancials);

// Tax reports
router.get("/store/:storeId/tax-report", authenticate, authorize(["admin", "store"]), generateTaxReport);

// Profit analysis
router.get("/store/:storeId/profit-analysis", authenticate, authorize(["admin", "store"]), getProfitAnalysis);

module.exports = router;
