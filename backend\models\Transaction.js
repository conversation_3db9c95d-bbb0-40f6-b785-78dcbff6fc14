const mongoose = require("mongoose");

const payfastDataSchema = new mongoose.Schema({
  paymentId: {
    type: String,
    required: false
  },
  merchantTransactionId: {
    type: String,
    required: false
  },
  signature: {
    type: String,
    required: false
  },
  paymentMethod: {
    type: String,
    required: false
  },
  cardType: {
    type: String,
    required: false
  },
  maskedCardNumber: {
    type: String,
    required: false
  }
});

const receiptSchema = new mongoose.Schema({
  generated: {
    type: Boolean,
    default: false
  },
  path: {
    type: String,
    required: false
  },
  emailSent: {
    type: Boolean,
    default: false
  },
  printed: {
    type: Boolean,
    default: false
  }
});

const transactionSchema = new mongoose.Schema({
  storeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Store",
    required: true,
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: false,
  },
  systemId: {
    type: String,
    required: false,
  },
  amount: {
    type: Number,
    required: true,
  },
  currency: {
    type: String,
    required: true,
    default: "ZAR"
  },
  status: {
    type: String,
    enum: ["pending", "completed", "failed", "refunded", "cancelled"],
    default: "pending",
  },
  transactionId: {
    type: String,
    required: true,
    unique: true
  },
  payfast: {
    type: payfastDataSchema,
    default: () => ({})
  },
  items: [{
    productId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Product",
      required: false
    },
    name: String,
    price: Number,
    quantity: Number,
    barcode: String
  }],
  receipt: {
    type: receiptSchema,
    default: () => ({})
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  completedAt: {
    type: Date,
    required: false
  }
});

module.exports = mongoose.model("Transaction", transactionSchema); 