import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Rating,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider
} from '@mui/material';
import {
  People,
  Schedule,
  ShoppingCart,
  TrendingUp,
  AccessTime,
  Star,
  ThumbUp,
  ThumbDown,
  Refresh,
  Download
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  Radar,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis
} from 'recharts';

const CustomerAnalytics = ({ storeId }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [customerData, setCustomerData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCustomerData();
  }, [storeId]);

  const fetchCustomerData = async () => {
    try {
      const response = await fetch(
        `/api/analytics/store/${storeId}/customers`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        }
      );
      const data = await response.json();
      setCustomerData(data);
    } catch (error) {
      console.error('Error fetching customer data:', error);
    } finally {
      setLoading(false);
    }
  };

  const MetricCard = ({ title, value, subtitle, icon, color = 'primary' }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" color={color}>
              {value}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                {subtitle}
              </Typography>
            )}
          </Box>
          <Box
            sx={{
              backgroundColor: `${color}.light`,
              borderRadius: 2,
              p: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  const ShoppingPatternsChart = ({ data }) => (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="hour" />
        <YAxis />
        <RechartsTooltip />
        <Bar dataKey="customers" fill="#8884d8" />
        <Bar dataKey="transactions" fill="#82ca9d" />
      </BarChart>
    </ResponsiveContainer>
  );

  const CustomerJourneyChart = ({ data }) => (
    <ResponsiveContainer width="100%" height={300}>
      <AreaChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="step" />
        <YAxis />
        <RechartsTooltip />
        <Area
          type="monotone"
          dataKey="customers"
          stroke="#8884d8"
          fill="#8884d8"
          fillOpacity={0.6}
        />
      </AreaChart>
    </ResponsiveContainer>
  );

  const SatisfactionRadar = ({ data }) => (
    <ResponsiveContainer width="100%" height={300}>
      <RadarChart data={data}>
        <PolarGrid />
        <PolarAngleAxis dataKey="aspect" />
        <PolarRadiusAxis angle={90} domain={[0, 5]} />
        <Radar
          name="Satisfaction"
          dataKey="score"
          stroke="#8884d8"
          fill="#8884d8"
          fillOpacity={0.6}
        />
        <RechartsTooltip />
      </RadarChart>
    </ResponsiveContainer>
  );

  const CustomerFeedback = ({ feedback }) => (
    <List>
      {feedback?.map((item, index) => (
        <React.Fragment key={index}>
          <ListItem alignItems="flex-start">
            <ListItemAvatar>
              <Avatar sx={{ bgcolor: item.rating >= 4 ? 'success.main' : item.rating >= 3 ? 'warning.main' : 'error.main' }}>
                {item.rating >= 4 ? <ThumbUp /> : item.rating >= 3 ? <Star /> : <ThumbDown />}
              </Avatar>
            </ListItemAvatar>
            <ListItemText
              primary={
                <Box display="flex" alignItems="center" gap={1}>
                  <Rating value={item.rating} readOnly size="small" />
                  <Typography variant="caption" color="textSecondary">
                    {item.date}
                  </Typography>
                </Box>
              }
              secondary={
                <Box>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    {item.comment}
                  </Typography>
                  <Box display="flex" gap={1} mt={1}>
                    {item.tags?.map((tag, tagIndex) => (
                      <Chip key={tagIndex} label={tag} size="small" variant="outlined" />
                    ))}
                  </Box>
                </Box>
              }
            />
          </ListItem>
          {index < feedback.length - 1 && <Divider variant="inset" component="li" />}
        </React.Fragment>
      ))}
    </List>
  );

  const CustomerSegments = ({ segments }) => (
    <Grid container spacing={2}>
      {segments?.map((segment, index) => (
        <Grid item xs={12} md={6} key={index}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {segment.name}
              </Typography>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                {segment.description}
              </Typography>
              <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                <Typography variant="h4" color="primary">
                  {segment.count}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  {segment.percentage}% of customers
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={segment.percentage}
                sx={{ mt: 1 }}
                color={index % 2 === 0 ? 'primary' : 'secondary'}
              />
              <Box mt={2}>
                <Typography variant="caption" color="textSecondary">
                  Avg. Basket: R{segment.avgBasket}
                </Typography>
                <br />
                <Typography variant="caption" color="textSecondary">
                  Visit Frequency: {segment.frequency}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  if (loading) {
    return <LinearProgress />;
  }

  // Mock data for demonstration
  const mockData = {
    totalCustomers: 1247,
    avgShoppingTime: '8.5 min',
    satisfactionScore: 4.2,
    returnRate: '68%',
    peakHours: [
      { hour: '08:00', customers: 45, transactions: 38 },
      { hour: '09:00', customers: 78, transactions: 65 },
      { hour: '10:00', customers: 92, transactions: 85 },
      { hour: '11:00', customers: 105, transactions: 98 },
      { hour: '12:00', customers: 134, transactions: 125 },
      { hour: '13:00', customers: 156, transactions: 142 },
      { hour: '14:00', customers: 143, transactions: 135 },
      { hour: '15:00', customers: 167, transactions: 158 },
      { hour: '16:00', customers: 189, transactions: 175 },
      { hour: '17:00', customers: 201, transactions: 192 },
      { hour: '18:00', customers: 178, transactions: 165 },
      { hour: '19:00', customers: 134, transactions: 125 }
    ],
    customerJourney: [
      { step: 'Enter Store', customers: 1000 },
      { step: 'Browse Products', customers: 950 },
      { step: 'Add to Cart', customers: 780 },
      { step: 'Self-Checkout', customers: 720 },
      { step: 'Payment', customers: 680 },
      { step: 'Complete Purchase', customers: 650 }
    ],
    satisfactionAspects: [
      { aspect: 'Ease of Use', score: 4.3 },
      { aspect: 'Speed', score: 4.1 },
      { aspect: 'Product Selection', score: 4.0 },
      { aspect: 'Price Accuracy', score: 4.5 },
      { aspect: 'Help Available', score: 3.8 },
      { aspect: 'Overall Experience', score: 4.2 }
    ],
    recentFeedback: [
      {
        rating: 5,
        comment: "Very easy to use! Love the self-checkout system.",
        date: "2 hours ago",
        tags: ["Easy", "Fast"]
      },
      {
        rating: 4,
        comment: "Good experience overall, but scanner was a bit slow.",
        date: "5 hours ago",
        tags: ["Scanner", "Speed"]
      },
      {
        rating: 3,
        comment: "System worked fine but needed help with payment.",
        date: "1 day ago",
        tags: ["Payment", "Help"]
      }
    ],
    customerSegments: [
      {
        name: "Regular Shoppers",
        description: "Customers who visit 2-3 times per week",
        count: 342,
        percentage: 27,
        avgBasket: 156,
        frequency: "2.5x/week"
      },
      {
        name: "Occasional Visitors",
        description: "Customers who visit once per week",
        count: 498,
        percentage: 40,
        avgBasket: 89,
        frequency: "1x/week"
      },
      {
        name: "Bulk Shoppers",
        description: "Customers with large basket sizes",
        count: 187,
        percentage: 15,
        avgBasket: 324,
        frequency: "1x/month"
      },
      {
        name: "Quick Shoppers",
        description: "Customers with small, frequent purchases",
        count: 220,
        percentage: 18,
        avgBasket: 45,
        frequency: "4x/week"
      }
    ]
  };

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Customer Analytics</Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<Download />}
            onClick={() => {/* Handle export */}}
          >
            Export Report
          </Button>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={fetchCustomerData}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total Customers"
            value={mockData.totalCustomers.toLocaleString()}
            subtitle="This month"
            icon={<People />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Avg Shopping Time"
            value={mockData.avgShoppingTime}
            subtitle="Per transaction"
            icon={<AccessTime />}
            color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Satisfaction Score"
            value={mockData.satisfactionScore}
            subtitle="Out of 5.0"
            icon={<Star />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Return Rate"
            value={mockData.returnRate}
            subtitle="Customer retention"
            icon={<TrendingUp />}
            color="warning"
          />
        </Grid>
      </Grid>

      {/* Tabs */}
      <Card>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="Shopping Patterns" />
          <Tab label="Customer Journey" />
          <Tab label="Satisfaction" />
          <Tab label="Customer Segments" />
        </Tabs>

        <CardContent>
          {activeTab === 0 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Peak Shopping Hours
              </Typography>
              <ShoppingPatternsChart data={mockData.peakHours} />
            </Box>
          )}

          {activeTab === 1 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Customer Journey Funnel
              </Typography>
              <CustomerJourneyChart data={mockData.customerJourney} />
              <Box mt={2}>
                <Typography variant="body2" color="textSecondary">
                  Conversion Rate: {((mockData.customerJourney[5].customers / mockData.customerJourney[0].customers) * 100).toFixed(1)}%
                </Typography>
              </Box>
            </Box>
          )}

          {activeTab === 2 && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Satisfaction by Aspect
                </Typography>
                <SatisfactionRadar data={mockData.satisfactionAspects} />
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Recent Feedback
                </Typography>
                <CustomerFeedback feedback={mockData.recentFeedback} />
              </Grid>
            </Grid>
          )}

          {activeTab === 3 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Customer Segments
              </Typography>
              <CustomerSegments segments={mockData.customerSegments} />
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default CustomerAnalytics;
