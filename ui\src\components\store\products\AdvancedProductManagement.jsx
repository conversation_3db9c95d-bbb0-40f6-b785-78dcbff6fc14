import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Alert,
  IconButton,
  Menu,
  MenuItem,
  Tooltip,
  Badge,
  LinearProgress,
  Avatar,
  Divider
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Warning,
  TrendingUp,
  TrendingDown,
  Inventory,
  PhotoCamera,
  MoreVert,
  FileUpload,
  Download,
  Refresh,
  Search,
  FilterList
} from '@mui/icons-material';
import { DataGrid } from '@mui/x-data-grid';

const AdvancedProductManagement = ({ storeId }) => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [alerts, setAlerts] = useState([]);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [bulkActionAnchor, setBulkActionAnchor] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');

  useEffect(() => {
    fetchProducts();
    fetchStockAlerts();
  }, [storeId]);

  const fetchProducts = async () => {
    try {
      const response = await fetch(`/api/products/store/${storeId}/advanced`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      setProducts(data.products || []);
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStockAlerts = async () => {
    try {
      const response = await fetch(`/api/products/store/${storeId}/alerts`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      setAlerts(data.alerts || []);
    } catch (error) {
      console.error('Error fetching alerts:', error);
    }
  };

  const StockAlert = ({ alert }) => (
    <Alert 
      severity={alert.level} 
      sx={{ mb: 1 }}
      action={
        <Button size="small" onClick={() => handleReorder(alert.productId)}>
          Reorder
        </Button>
      }
    >
      <strong>{alert.productName}</strong> - {alert.message}
    </Alert>
  );

  const ProductPerformanceCard = ({ product }) => {
    const performance = product.performance || {};
    const trend = performance.trend || 0;
    
    return (
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Box display="flex" alignItems="center" mb={2}>
            <Avatar
              src={product.image}
              sx={{ width: 60, height: 60, mr: 2 }}
            >
              <Inventory />
            </Avatar>
            <Box flex={1}>
              <Typography variant="h6">{product.name}</Typography>
              <Typography variant="body2" color="textSecondary">
                {product.barcode}
              </Typography>
            </Box>
            <Box textAlign="right">
              <Typography variant="h6" color="primary">
                R{product.price}
              </Typography>
              <Box display="flex" alignItems="center">
                {trend >= 0 ? (
                  <TrendingUp color="success" fontSize="small" />
                ) : (
                  <TrendingDown color="error" fontSize="small" />
                )}
                <Typography
                  variant="body2"
                  color={trend >= 0 ? 'success.main' : 'error.main'}
                  sx={{ ml: 0.5 }}
                >
                  {Math.abs(trend)}%
                </Typography>
              </Box>
            </Box>
          </Box>

          <Grid container spacing={2}>
            <Grid item xs={3}>
              <Typography variant="caption" color="textSecondary">
                Stock Level
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                {product.stockLevel || 0}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={Math.min((product.stockLevel / product.reorderPoint) * 100, 100)}
                color={product.stockLevel < product.reorderPoint ? 'error' : 'success'}
                sx={{ mt: 0.5 }}
              />
            </Grid>
            <Grid item xs={3}>
              <Typography variant="caption" color="textSecondary">
                Sales (7d)
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                {performance.weekSales || 0}
              </Typography>
            </Grid>
            <Grid item xs={3}>
              <Typography variant="caption" color="textSecondary">
                Revenue (7d)
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                R{performance.weekRevenue || 0}
              </Typography>
            </Grid>
            <Grid item xs={3}>
              <Typography variant="caption" color="textSecondary">
                Velocity
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                {performance.velocity || 0}/day
              </Typography>
            </Grid>
          </Grid>

          <Box mt={2} display="flex" gap={1}>
            <Chip
              label={`Category: ${product.category || 'Uncategorized'}`}
              size="small"
              variant="outlined"
            />
            {product.stockLevel < product.reorderPoint && (
              <Chip
                label="Low Stock"
                size="small"
                color="error"
                icon={<Warning />}
              />
            )}
            {performance.trending && (
              <Chip
                label="Trending"
                size="small"
                color="success"
                icon={<TrendingUp />}
              />
            )}
          </Box>
        </CardContent>
      </Card>
    );
  };

  const BulkActionsMenu = () => (
    <Menu
      anchorEl={bulkActionAnchor}
      open={Boolean(bulkActionAnchor)}
      onClose={() => setBulkActionAnchor(null)}
    >
      <MenuItem onClick={handleBulkPriceUpdate}>
        <Edit sx={{ mr: 1 }} />
        Update Prices
      </MenuItem>
      <MenuItem onClick={handleBulkCategoryUpdate}>
        <FilterList sx={{ mr: 1 }} />
        Update Category
      </MenuItem>
      <MenuItem onClick={handleBulkStockUpdate}>
        <Inventory sx={{ mr: 1 }} />
        Update Stock
      </MenuItem>
      <MenuItem onClick={handleBulkDelete} sx={{ color: 'error.main' }}>
        <Delete sx={{ mr: 1 }} />
        Delete Products
      </MenuItem>
    </Menu>
  );

  const handleBulkPriceUpdate = () => {
    // Implementation for bulk price update
    setBulkActionAnchor(null);
  };

  const handleBulkCategoryUpdate = () => {
    // Implementation for bulk category update
    setBulkActionAnchor(null);
  };

  const handleBulkStockUpdate = () => {
    // Implementation for bulk stock update
    setBulkActionAnchor(null);
  };

  const handleBulkDelete = () => {
    // Implementation for bulk delete
    setBulkActionAnchor(null);
  };

  const handleReorder = (productId) => {
    // Implementation for reorder
    console.log('Reorder product:', productId);
  };

  const columns = [
    {
      field: 'image',
      headerName: '',
      width: 60,
      renderCell: (params) => (
        <Avatar src={params.row.image} sx={{ width: 40, height: 40 }}>
          <Inventory />
        </Avatar>
      )
    },
    { field: 'name', headerName: 'Product Name', flex: 1 },
    { field: 'barcode', headerName: 'Barcode', width: 120 },
    { 
      field: 'price', 
      headerName: 'Price', 
      width: 100,
      renderCell: (params) => `R${params.value}`
    },
    {
      field: 'stockLevel',
      headerName: 'Stock',
      width: 100,
      renderCell: (params) => (
        <Box display="flex" alignItems="center">
          <Typography variant="body2">
            {params.value || 0}
          </Typography>
          {params.row.stockLevel < params.row.reorderPoint && (
            <Warning color="error" fontSize="small" sx={{ ml: 1 }} />
          )}
        </Box>
      )
    },
    {
      field: 'performance',
      headerName: 'Performance',
      width: 120,
      renderCell: (params) => {
        const trend = params.row.performance?.trend || 0;
        return (
          <Box display="flex" alignItems="center">
            {trend >= 0 ? (
              <TrendingUp color="success" fontSize="small" />
            ) : (
              <TrendingDown color="error" fontSize="small" />
            )}
            <Typography variant="body2" sx={{ ml: 0.5 }}>
              {Math.abs(trend)}%
            </Typography>
          </Box>
        );
      }
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      renderCell: (params) => (
        <Box>
          <IconButton size="small" onClick={() => handleEditProduct(params.row)}>
            <Edit />
          </IconButton>
          <IconButton size="small" onClick={() => handleDeleteProduct(params.row.id)}>
            <Delete />
          </IconButton>
        </Box>
      )
    }
  ];

  const handleEditProduct = (product) => {
    // Implementation for edit product
    console.log('Edit product:', product);
  };

  const handleDeleteProduct = (productId) => {
    // Implementation for delete product
    console.log('Delete product:', productId);
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.barcode.includes(searchTerm);
    const matchesCategory = filterCategory === 'all' || product.category === filterCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Product Management</Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<FileUpload />}
            onClick={() => {/* Handle bulk import */}}
          >
            Import Products
          </Button>
          <Button
            variant="outlined"
            startIcon={<Download />}
            onClick={() => {/* Handle export */}}
          >
            Export
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => {/* Handle add product */}}
          >
            Add Product
          </Button>
        </Box>
      </Box>

      {/* Stock Alerts */}
      {alerts.length > 0 && (
        <Box mb={3}>
          <Typography variant="h6" gutterBottom>
            <Badge badgeContent={alerts.length} color="error">
              Stock Alerts
            </Badge>
          </Typography>
          {alerts.map((alert, index) => (
            <StockAlert key={index} alert={alert} />
          ))}
        </Box>
      )}

      {/* Search and Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                select
                label="Category"
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
              >
                <MenuItem value="all">All Categories</MenuItem>
                <MenuItem value="food">Food</MenuItem>
                <MenuItem value="beverages">Beverages</MenuItem>
                <MenuItem value="household">Household</MenuItem>
                <MenuItem value="personal-care">Personal Care</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12} md={3}>
              {selectedProducts.length > 0 && (
                <Button
                  variant="outlined"
                  onClick={(e) => setBulkActionAnchor(e.currentTarget)}
                  endIcon={<MoreVert />}
                >
                  Bulk Actions ({selectedProducts.length})
                </Button>
              )}
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Refresh />}
                onClick={fetchProducts}
              >
                Refresh
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Product Grid */}
      <Card>
        <CardContent>
          <DataGrid
            rows={filteredProducts}
            columns={columns}
            pageSize={25}
            rowsPerPageOptions={[25, 50, 100]}
            checkboxSelection
            disableSelectionOnClick
            loading={loading}
            onSelectionModelChange={(newSelection) => {
              setSelectedProducts(newSelection);
            }}
            sx={{ height: 600 }}
          />
        </CardContent>
      </Card>

      <BulkActionsMenu />
    </Box>
  );
};

export default AdvancedProductManagement;
