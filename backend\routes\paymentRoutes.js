const express = require("express");
const { authenticate, authorize } = require("../middleware/authMiddleware");
const {
  initiatePayment,
  handleWebhook,
  checkPaymentStatus,
  processRefund,
  getPaymentHistory
} = require("../controllers/payfastController");

const router = express.Router();

// Payment initiation endpoint
router.post("/initiate", authenticate, initiatePayment);

// PayFast webhook endpoint (no authentication required)
router.post("/webhook", handleWebhook);

// Payment status check
router.get("/status/:transactionId", authenticate, checkPaymentStatus);

// Process refund
router.post("/refund", authenticate, authorize(["admin", "store"]), processRefund);

// Get payment history for a store
router.get("/history/:storeId", authenticate, authorize(["admin", "store"]), getPaymentHistory);

// Return URL handler (for web-based payments)
router.get("/return", (req, res) => {
  const { payment_status, m_payment_id } = req.query;
  
  // Redirect to success/failure page based on payment status
  if (payment_status === 'COMPLETE') {
    res.redirect(`/payment/success?transaction=${m_payment_id}`);
  } else {
    res.redirect(`/payment/failed?transaction=${m_payment_id}`);
  }
});

// Cancel URL handler
router.get("/cancel", (req, res) => {
  const { m_payment_id } = req.query;
  res.redirect(`/payment/cancelled?transaction=${m_payment_id}`);
});

module.exports = router;
