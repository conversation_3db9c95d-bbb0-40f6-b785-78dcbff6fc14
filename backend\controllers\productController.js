const Product = require("../models/Product");
const Store = require("../models/Store");
const fs = require("fs");
const path = require("path");
const generateQRCode = require("../utils/qrCodeGenerator");
const PDFDocument = require('pdfkit');

// Add a new product with optional image upload
exports.addProduct = async (req, res) => {
  try {
    const { barcode, name, price, description, storeId, storeName } = req.body;

    // Store users can only add products for their store
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied. Unauthorized store." });
    }

    // Create product object with optional image
    const productData = {
      barcode,
      name,
      price,
      description,
      storeId,
    };

    // Add image path if an image was uploaded
    if (req.file) {
      productData.image = req.file.path;
    }

    const product = new Product(productData);
    await product.save();

    res.status(201).json({ message: "Product added successfully", product });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// Update a product with optional image upload
exports.updateProduct = async (req, res) => {
  try {
    const { id } = req.params;
    const { barcode, name, price, description, storeName } = req.body;

    const product = await Product.findById(id);

    if (!product) {
      return res.status(404).json({ message: "Product not found" });
    }

    // Store users can only update products for their store
    if (req.user.role === "store" && req.user.storeId.toString() !== product.storeId.toString()) {
      return res.status(403).json({ message: "Access denied. Unauthorized store." });
    }

    // If a new image is uploaded, delete the old image and update the path
    if (req.file) {
      // Delete the old image file if it exists
      if (product.image && fs.existsSync(product.image)) {
        fs.unlinkSync(product.image);
      }

      // Update the image path
      product.image = req.file.path;
    }

    // Update other product details
    product.barcode = barcode;
    product.name = name;
    product.price = price;
    product.description = description;

    await product.save();

    res.status(200).json({ message: "Product updated successfully", product });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// Add a new method to handle payment confirmation
exports.confirmPayment = async (req, res) => {
  try {
    const { transactionId, status } = req.body;

    // Logic to update the payment status in the database
    // This could involve updating a specific order or transaction record

    res.status(200).json({ message: "Payment status updated successfully" });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// Add a new method to handle receipt generation
exports.generateReceipt = async (req, res) => {
  try {
    const { products, totalAmount } = req.body;

    // Create a PDF document
    const doc = new PDFDocument();
    const receiptPath = `./receipts/receipt_${Date.now()}.pdf`;
    doc.pipe(fs.createWriteStream(receiptPath));

    // Add content to the PDF
    doc.fontSize(25).text('Receipt', { align: 'center' });
    doc.moveDown();
    doc.fontSize(12).text(`Date: ${new Date().toLocaleString()}`);
    doc.moveDown();
    doc.text('Products:');
    products.forEach(product => {
      doc.text(`- ${product.name}: $${product.price}`);
    });
    doc.moveDown();
    doc.text(`Total Amount: $${totalAmount}`);
    doc.end();

    res.status(200).json({ message: "Receipt generated", receiptPath });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// Get products for a specific store
exports.getProducts = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Ensure the user is authorized to access the store's products
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied. Unauthorized store." });
    }

    const products = await Product.find({ storeId });
    res.status(200).json(products);
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// Add this function if it's missing
exports.deleteProduct = async (req, res) => {
  try {
    const { id } = req.params;
    const product = await Product.findByIdAndDelete(id);

    if (!product) {
      return res.status(404).json({ message: "Product not found" });
    }

    res.status(200).json({ message: "Product deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: "Server error", error });
  }
};

// Add this new method
exports.scanBarcode = async (req, res) => {
    try {
        const { barcode } = req.body;
        
        // Find product by barcode
        const product = await Product.findOne({ barcode });
        
        if (!product) {
            return res.status(404).json({ message: "Product not found" });
        }

        res.status(200).json({
            name: product.name,
            price: product.price,
            description: product.description,
            image: product.image
        });
    } catch (error) {
        res.status(500).json({ message: "Server error", error });
    }
};

// Get advanced product data with analytics
exports.getAdvancedProducts = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Get products with performance data
    const products = await Product.aggregate([
      { $match: { storeId: require('mongoose').Types.ObjectId(storeId) } },
      {
        $lookup: {
          from: 'transactions',
          let: { productName: '$name' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$storeId', require('mongoose').Types.ObjectId(storeId)] },
                    { $eq: ['$status', 'completed'] },
                    { $gte: ['$createdAt', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)] }
                  ]
                }
              }
            },
            { $unwind: '$items' },
            {
              $match: {
                $expr: { $eq: ['$items.name', '$$productName'] }
              }
            },
            {
              $group: {
                _id: null,
                weekSales: { $sum: '$items.quantity' },
                weekRevenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } }
              }
            }
          ],
          as: 'performance'
        }
      },
      {
        $addFields: {
          performance: {
            $let: {
              vars: { perf: { $arrayElemAt: ['$performance', 0] } },
              in: {
                weekSales: { $ifNull: ['$$perf.weekSales', 0] },
                weekRevenue: { $ifNull: ['$$perf.weekRevenue', 0] },
                velocity: { $divide: [{ $ifNull: ['$$perf.weekSales', 0] }, 7] },
                trend: { $multiply: [{ $rand: {} }, 20] } // Mock trend data
              }
            }
          },
          stockLevel: { $multiply: [{ $rand: {} }, 100] }, // Mock stock data
          reorderPoint: 20 // Mock reorder point
        }
      }
    ]);

    res.status(200).json({ products });

  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// Get stock alerts
exports.getStockAlerts = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Mock stock alerts - in production, this would check actual inventory
    const alerts = [
      {
        productId: '1',
        productName: 'Coca Cola 330ml',
        message: 'Stock level is below reorder point (5 remaining)',
        level: 'warning',
        stockLevel: 5,
        reorderPoint: 20
      },
      {
        productId: '2',
        productName: 'Bread White Loaf',
        message: 'Out of stock - immediate reorder required',
        level: 'error',
        stockLevel: 0,
        reorderPoint: 10
      }
    ];

    res.status(200).json({ alerts });

  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// Bulk update products
exports.bulkUpdateProducts = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { productIds, updates, operation } = req.body;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    let result;

    switch (operation) {
      case 'updatePrices':
        result = await Product.updateMany(
          {
            _id: { $in: productIds },
            storeId: storeId
          },
          {
            $mul: { price: updates.priceMultiplier || 1 },
            $inc: { price: updates.priceAdjustment || 0 }
          }
        );
        break;

      case 'updateCategory':
        result = await Product.updateMany(
          {
            _id: { $in: productIds },
            storeId: storeId
          },
          { $set: { category: updates.category } }
        );
        break;

      case 'updateStock':
        // In production, this would update actual inventory system
        result = { modifiedCount: productIds.length };
        break;

      case 'delete':
        result = await Product.deleteMany({
          _id: { $in: productIds },
          storeId: storeId
        });
        break;

      default:
        return res.status(400).json({ message: 'Invalid operation' });
    }

    res.status(200).json({
      message: `Bulk ${operation} completed successfully`,
      modifiedCount: result.modifiedCount || result.deletedCount,
      operation
    });

  } catch (error) {
    res.status(500).json({ message: "Bulk operation failed", error: error.message });
  }
};