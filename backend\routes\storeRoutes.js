const express = require("express");
const { authenticate, authorize } = require("../middleware/authMiddleware");
const {
  addStore,
  updateStore,
  deleteStore,
  getStores,
  uploadFlyer,
  getFlyer,
  configurePayFast,
  getPayFastConfig,
  testPayFastConnection,
  togglePayFastStatus
} = require("../controllers/storeController");
const upload = require("../config/multerConfig"); // Ensure multer is imported
const Store = require("../models/Store");
const path = require("path");
const fs = require("fs");
const bcrypt = require('bcryptjs');
const auth = require('../middleware/auth');

const router = express.Router();

// Only admins can add, update, or delete stores
router.post("/", authenticate, authorize(["admin"]), addStore);
router.put("/:id", authenticate, authorize(["admin", "store"]), async (req, res) => {
    try {
        const { id } = req.params;
        const { systems } = req.body;

        // For store users, verify they can only update their own store
        if (req.user.role === "store" && req.user.storeId.toString() !== id) {
            return res.status(403).json({ message: "Access denied. Can only update your own store." });
        }

        const store = await Store.findByIdAndUpdate(
            id, 
            { systems }, 
            { new: true }
        );

        if (!store) {
            return res.status(404).json({ message: "Store not found" });
        }

        res.status(200).json({ message: "Store updated successfully", store });
    } catch (error) {
        res.status(500).json({ message: "Server error", error });
    }
});
router.delete("/:id", authenticate, authorize(["admin"]), deleteStore);

// Upload flyer for a specific store
router.post("/:id/upload-flyer", authenticate, authorize(["admin", "store"]), upload.single("flyer"), async (req, res) => {
    try {
        const { id } = req.params;

        // Verify user has permission for this store
        if (req.user.role === 'store' && req.user.storeId.toString() !== id) {
            return res.status(403).json({ message: "Unauthorized: You can only upload flyers to your own store" });
        }

        if (!req.file) {
            return res.status(400).json({ message: "No file uploaded" });
        }

        // Use findOneAndUpdate instead of findById to avoid validation issues
        const store = await Store.findOneAndUpdate(
            { _id: id },
            { flyer: req.file.path.replace(/\\/g, '/') },
            { 
                new: true,
                runValidators: false // Disable validation since we're only updating the flyer
            }
        );

        if (!store) {
            return res.status(404).json({ message: "Store not found" });
        }

        res.status(200).json({
            message: "Flyer uploaded successfully",
            flyer: store.flyer
        });
    } catch (error) {
        console.error('Error uploading flyer:', error);
        res.status(500).json({ message: "Server error", error: error.message });
    }
});

// Get flyer for a specific store
router.get("/:id/flyer", authenticate, async (req, res) => {
    try {
        const { id } = req.params;
        
        // Find the store
        const store = await Store.findById(id);
        if (!store) {
            return res.status(404).json({ message: "Store not found" });
        }

        // If no flyer exists
        if (!store.flyer) {
            return res.status(404).json({ message: "No flyer found for this store" });
        }

        res.status(200).json({ flyer: store.flyer });
    } catch (error) {
        console.error('Error fetching flyer:', error);
        res.status(500).json({ message: "Server error", error: error.message });
    }
});

// Both admins and store users can view stores
router.get("/", authenticate, getStores);

// Add this new route to get systems for a store
router.get("/:id/systems", authenticate, authorize(["admin", "store"]), async (req, res) => {
    try {
        const { id } = req.params;

        // For store users, verify they can only access their own store
        if (req.user.role === "store" && req.user.storeId.toString() !== id) {
            return res.status(403).json({ message: "Access denied" });
        }

        const store = await Store.findById(id);
        if (!store) {
            return res.status(404).json({ message: "Store not found" });
        }

        res.status(200).json({ systems: store.systems || [] });
    } catch (error) {
        res.status(500).json({ message: "Server error", error: error.message });
    }
});

// Update systems for a store
router.put("/:id/systems", authenticate, authorize(["admin", "store"]), async (req, res) => {
    try {
        const { id } = req.params;
        const { systems } = req.body;

        // For store users, verify they can only update their own store
        if (req.user.role === "store" && req.user.storeId.toString() !== id) {
            return res.status(403).json({ message: "Access denied" });
        }

        const store = await Store.findByIdAndUpdate(
            id,
            { systems },
            { new: true }
        );

        if (!store) {
            return res.status(404).json({ message: "Store not found" });
        }

        res.status(200).json({ message: "Systems updated successfully", systems: store.systems });
    } catch (error) {
        res.status(500).json({ message: "Server error", error: error.message });
    }
});

// Create store with credentials
router.post('/', auth, async (req, res) => {
  try {
    const { name, email, password } = req.body;

    // Validate input
    if (!name || !email || !password) {
      return res.status(400).json({ message: 'Please provide all required fields' });
    }

    // Check if email already exists
    const existingStore = await Store.findOne({ email });
    if (existingStore) {
      return res.status(400).json({ message: 'Email already registered' });
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create store
    const store = new Store({
      name,
      email,
      password: hashedPassword,
      systems: []
    });

    await store.save();

    // Don't send password in response
    const storeResponse = store.toObject();
    delete storeResponse.password;

    res.status(201).json(storeResponse);
  } catch (error) {
    console.error('Store creation error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update store
router.put('/:id', auth, async (req, res) => {
  try {
    const { name, email, password } = req.body;
    const storeId = req.params.id;

    const updateData = { name, email };

    // Only update password if provided
    if (password) {
      const salt = await bcrypt.genSalt(10);
      updateData.password = await bcrypt.hash(password, salt);
    }

    // Check if email is being changed and if it's already in use
    if (email) {
      const existingStore = await Store.findOne({ email, _id: { $ne: storeId } });
      if (existingStore) {
        return res.status(400).json({ message: 'Email already in use' });
      }
    }

    const store = await Store.findByIdAndUpdate(
      storeId,
      { $set: updateData },
      { new: true }
    ).select('-password');

    if (!store) {
      return res.status(404).json({ message: 'Store not found' });
    }

    res.json(store);
  } catch (error) {
    console.error('Store update error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Change store password
router.post('/change-password', auth, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const storeId = req.store.id; // From auth middleware

    const store = await Store.findById(storeId);
    if (!store) {
      return res.status(404).json({ message: 'Store not found' });
    }

    // Verify current password
    const isMatch = await bcrypt.compare(currentPassword, store.password);
    if (!isMatch) {
      return res.status(400).json({ message: 'Current password is incorrect' });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update password
    store.password = hashedPassword;
    await store.save();

    res.json({ message: 'Password updated successfully' });
  } catch (error) {
    console.error('Password change error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get store systems
router.get('/:id/systems', auth, async (req, res) => {
  try {
    const store = await Store.findById(req.params.id).select('systems');
    if (!store) {
      return res.status(404).json({ message: 'Store not found' });
    }

    res.json({ systems: store.systems });
  } catch (error) {
    console.error('Fetch systems error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update store systems
router.put('/:id/systems', auth, async (req, res) => {
  try {
    const { systems } = req.body;
    const store = await Store.findByIdAndUpdate(
      req.params.id,
      { $set: { systems } },
      { new: true }
    ).select('systems');

    if (!store) {
      return res.status(404).json({ message: 'Store not found' });
    }

    res.json({ systems: store.systems });
  } catch (error) {
    console.error('Update systems error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Add new route for deleting flyer
router.delete("/:id/flyer", authenticate, async (req, res) => {
  try {
    const { id } = req.params;

    // Verify user has permission for this store
    if (req.user.role === 'store' && req.user.storeId.toString() !== id) {
      return res.status(403).json({ message: "Unauthorized: You can only manage your own store's flyer" });
    }

    const store = await Store.findById(id);
    if (!store) {
      return res.status(404).json({ message: "Store not found" });
    }

    // Delete the flyer file if it exists
    if (store.flyer) {
      const flyerPath = path.join(__dirname, '..', store.flyer);
      if (fs.existsSync(flyerPath)) {
        fs.unlinkSync(flyerPath);
      }
      
      // Clear the flyer field in the database
      store.flyer = null;
      await store.save();
    }

    res.status(200).json({ message: "Flyer deleted successfully" });
  } catch (error) {
    console.error('Error deleting flyer:', error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
});

// PayFast configuration routes
router.put("/:id/payfast", authenticate, authorize(["admin", "store"]), configurePayFast);
router.get("/:id/payfast", authenticate, authorize(["admin", "store"]), getPayFastConfig);
router.get("/:id/payfast/test", authenticate, authorize(["admin", "store"]), testPayFastConnection);
router.put("/:id/payfast/toggle", authenticate, authorize(["admin", "store"]), togglePayFastStatus);

module.exports = router;