# Self-Checkout Frontend Dockerfile
# Multi-stage build for optimized production image

# Build stage
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine

# Install curl for health checks
RUN apk add --no-cache curl

# Copy built application
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Create non-root user
RUN addgroup -g 1001 -S nginx && \
    adduser -S selfcheckout -u 1001 -G nginx

# Set proper permissions
RUN chown -R selfcheckout:nginx /usr/share/nginx/html && \
    chown -R selfcheckout:nginx /var/cache/nginx && \
    chown -R selfcheckout:nginx /var/log/nginx && \
    chown -R selfcheckout:nginx /etc/nginx/conf.d

# Switch to non-root user
USER selfcheckout

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80 || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
