#!/bin/bash
# Self-Checkout Backend Installation Script
# Usage: chmod +x install-backend.sh && ./install-backend.sh

set -e  # Exit on any error

echo "🚀 Installing Self-Checkout Backend..."

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo "❌ This script should not be run as root"
   exit 1
fi

# Detect OS
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    OS="windows"
else
    echo "❌ Unsupported operating system: $OSTYPE"
    exit 1
fi

echo "📋 Detected OS: $OS"

# Install Node.js if not present
if ! command -v node &> /dev/null; then
    echo "📦 Installing Node.js..."
    
    if [[ "$OS" == "linux" ]]; then
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt-get install -y nodejs
    elif [[ "$OS" == "macos" ]]; then
        if command -v brew &> /dev/null; then
            brew install node
        else
            echo "❌ Please install Homebrew first: https://brew.sh/"
            exit 1
        fi
    elif [[ "$OS" == "windows" ]]; then
        echo "❌ Please install Node.js manually from https://nodejs.org/"
        exit 1
    fi
else
    echo "✅ Node.js already installed: $(node --version)"
fi

# Install MongoDB if not present
if ! command -v mongod &> /dev/null; then
    echo "📦 Installing MongoDB..."
    
    if [[ "$OS" == "linux" ]]; then
        wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
        echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
        sudo apt-get update
        sudo apt-get install -y mongodb-org
        sudo systemctl start mongod
        sudo systemctl enable mongod
    elif [[ "$OS" == "macos" ]]; then
        if command -v brew &> /dev/null; then
            brew tap mongodb/brew
            brew install mongodb-community
            brew services start mongodb/brew/mongodb-community
        else
            echo "❌ Please install Homebrew first: https://brew.sh/"
            exit 1
        fi
    elif [[ "$OS" == "windows" ]]; then
        echo "❌ Please install MongoDB manually from https://www.mongodb.com/try/download/community"
        exit 1
    fi
else
    echo "✅ MongoDB already installed"
fi

# Check if we're in the correct directory
if [[ ! -f "package.json" ]]; then
    echo "❌ package.json not found. Please run this script from the backend directory."
    exit 1
fi

# Install npm dependencies
echo "📦 Installing npm dependencies..."
npm install

# Create environment file if it doesn't exist
if [[ ! -f ".env" ]]; then
    echo "📝 Creating environment file..."
    
    # Generate random JWT secret
    JWT_SECRET=$(openssl rand -base64 32 2>/dev/null || python3 -c "import secrets; print(secrets.token_urlsafe(32))" 2>/dev/null || echo "your_jwt_secret_here_please_change_this")
    
    cat > .env << EOF
# Database Configuration
MONGODB_URI=mongodb://localhost:27017/selfcheckout

# Security
JWT_SECRET=$JWT_SECRET

# Server Configuration
PORT=5000
NODE_ENV=development
BASE_URL=http://localhost:5000

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# PayFast Configuration (will be set per store)
PAYFAST_SANDBOX=true
EOF
    
    echo "✅ Environment file created"
else
    echo "✅ Environment file already exists"
fi

# Create uploads directory
mkdir -p uploads

# Test MongoDB connection
echo "🔍 Testing MongoDB connection..."
if node -e "
const mongoose = require('mongoose');
mongoose.connect('mongodb://localhost:27017/selfcheckout')
  .then(() => { console.log('✅ MongoDB connection successful'); process.exit(0); })
  .catch((err) => { console.log('❌ MongoDB connection failed:', err.message); process.exit(1); });
" 2>/dev/null; then
    echo "✅ MongoDB connection test passed"
else
    echo "⚠️  MongoDB connection test failed. Please check MongoDB installation."
fi

# Install PM2 for production
if ! command -v pm2 &> /dev/null; then
    echo "📦 Installing PM2 for production deployment..."
    npm install -g pm2
fi

echo ""
echo "🎉 Backend installation completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Review and update .env file with your configuration"
echo "2. Run 'npm run seed' to initialize the database"
echo "3. Run 'npm start' to start the development server"
echo "4. For production: 'pm2 start server.js --name selfcheckout-api'"
echo ""
echo "🌐 API will be available at: http://localhost:5000"
echo "📚 API documentation: http://localhost:5000/api-docs"
echo ""
echo "🔧 Configuration file: .env"
echo "📁 Upload directory: ./uploads"
echo ""
