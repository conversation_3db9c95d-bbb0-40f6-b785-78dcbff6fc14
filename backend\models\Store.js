const mongoose = require("mongoose");

const systemSchema = new mongoose.Schema({
  systemId: {
    type: String,
    required: true,
    unique: true
  },
  location: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: false
  },
  status: {
    type: String,
    enum: ['Active', 'Inactive', 'Maintenance'],
    default: 'Active'
  },
  registeredAt: {
    type: Date,
    default: Date.now
  },
  lastSeen: {
    type: Date,
    required: false
  }
});

const payfastSchema = new mongoose.Schema({
  merchantId: {
    type: String,
    required: false
  },
  merchantKey: {
    type: String,
    required: false
  },
  passphrase: {
    type: String,
    required: false
  },
  sandbox: {
    type: Boolean,
    default: true
  },
  posDeviceId: {
    type: String,
    required: false
  },
  isActive: {
    type: Boolean,
    default: false
  },
  setupComplete: {
    type: Boolean,
    default: false
  }
});

const storeSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    required: true,
    unique: true
  },
  password: {
    type: String,
    required: true
  },
  systems: [systemSchema],
  flyer: {
    type: String,
  },
  payfast: {
    type: payfastSchema,
    default: () => ({})
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model("Store", storeSchema);