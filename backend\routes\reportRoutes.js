const express = require("express");
const { authenticate, authorize } = require("../middleware/authMiddleware");
const {
  getStoreReports,
  getAdminReports,
  getTransactionDetails,
  exportSalesData
} = require("../controllers/reportController");

const router = express.Router();

// Store-specific reports
router.get("/stores/:storeId", authenticate, authorize(["admin", "store"]), getStoreReports);

// Admin reports (cross-store analytics)
router.get("/admin", authenticate, authorize(["admin"]), getAdminReports);

// Transaction details
router.get("/transactions/:transactionId", authenticate, authorize(["admin", "store"]), getTransactionDetails);

// Export sales data
router.get("/export", authenticate, authorize(["admin", "store"]), exportSalesData);

module.exports = router;
