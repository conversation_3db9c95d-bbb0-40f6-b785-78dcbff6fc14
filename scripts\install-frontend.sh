#!/bin/bash
# Self-Checkout Frontend Installation Script
# Usage: chmod +x install-frontend.sh && ./install-frontend.sh

set -e  # Exit on any error

echo "🌐 Installing Self-Checkout Frontend..."

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo "❌ This script should not be run as root"
   exit 1
fi

# Detect OS
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    OS="windows"
else
    echo "❌ Unsupported operating system: $OSTYPE"
    exit 1
fi

echo "📋 Detected OS: $OS"

# Install Node.js if not present
if ! command -v node &> /dev/null; then
    echo "📦 Installing Node.js..."
    
    if [[ "$OS" == "linux" ]]; then
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt-get install -y nodejs
    elif [[ "$OS" == "macos" ]]; then
        if command -v brew &> /dev/null; then
            brew install node
        else
            echo "❌ Please install Homebrew first: https://brew.sh/"
            exit 1
        fi
    elif [[ "$OS" == "windows" ]]; then
        echo "❌ Please install Node.js manually from https://nodejs.org/"
        exit 1
    fi
else
    echo "✅ Node.js already installed: $(node --version)"
fi

# Check if we're in the correct directory
if [[ ! -f "package.json" ]]; then
    echo "❌ package.json not found. Please run this script from the ui directory."
    exit 1
fi

# Install npm dependencies
echo "📦 Installing npm dependencies..."
npm install

# Create environment file if it doesn't exist
if [[ ! -f ".env.local" ]]; then
    echo "📝 Creating environment file..."
    
    # Prompt for API URL
    read -p "Enter your API URL (default: http://localhost:5000/api): " API_URL
    API_URL=${API_URL:-"http://localhost:5000/api"}
    
    cat > .env.local << EOF
# API Configuration
VITE_API_URL=$API_URL

# Development Configuration
VITE_NODE_ENV=development
EOF
    
    echo "✅ Environment file created"
else
    echo "✅ Environment file already exists"
fi

# Build for production
echo "🏗️  Building for production..."
npm run build

# Install serve for production serving (optional)
if ! command -v serve &> /dev/null; then
    echo "📦 Installing serve for production deployment..."
    npm install -g serve
fi

# Create production start script
echo "📝 Creating production scripts..."

cat > start-production.sh << 'EOF'
#!/bin/bash
# Start production server
echo "🚀 Starting Self-Checkout Frontend (Production)..."
serve -s dist -l 3000
EOF

chmod +x start-production.sh

cat > start-development.sh << 'EOF'
#!/bin/bash
# Start development server
echo "🚀 Starting Self-Checkout Frontend (Development)..."
npm run dev
EOF

chmod +x start-development.sh

# Create nginx configuration template
echo "📝 Creating nginx configuration template..."
cat > nginx.conf.template << 'EOF'
server {
    listen 80;
    server_name your-domain.com;
    
    root /path/to/your/dist;
    index index.html;
    
    # Handle client-side routing
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API proxy (optional)
    location /api/ {
        proxy_pass http://localhost:5000/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;
}
EOF

# Create Apache configuration template
echo "📝 Creating Apache configuration template..."
cat > apache.conf.template << 'EOF'
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/your/dist
    
    <Directory /path/to/your/dist>
        Options -Indexes
        AllowOverride All
        Require all granted
        
        # Handle client-side routing
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
    
    # API proxy (optional)
    ProxyPreserveHost On
    ProxyPass /api/ http://localhost:5000/api/
    ProxyPassReverse /api/ http://localhost:5000/api/
    
    # Security headers
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Content-Type-Options "nosniff"
    Header always set Referrer-Policy "no-referrer-when-downgrade"
    
    # Compression
    LoadModule deflate_module modules/mod_deflate.so
    <Location />
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png)$ no-gzip dont-vary
        SetEnvIfNoCase Request_URI \
            \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    </Location>
</VirtualHost>
EOF

# Create deployment script
cat > deploy.sh << 'EOF'
#!/bin/bash
# Deployment script for Self-Checkout Frontend

echo "🚀 Deploying Self-Checkout Frontend..."

# Build the application
echo "🏗️  Building application..."
npm run build

# Create deployment package
echo "📦 Creating deployment package..."
tar -czf selfcheckout-frontend-$(date +%Y%m%d_%H%M%S).tar.gz dist/

echo "✅ Deployment package created!"
echo "📁 Upload the dist/ folder to your web server"
echo "⚙️  Configure your web server using the provided templates:"
echo "   - nginx.conf.template"
echo "   - apache.conf.template"
EOF

chmod +x deploy.sh

# Test build
echo "🔍 Testing build..."
if [[ -d "dist" && -f "dist/index.html" ]]; then
    echo "✅ Build test passed"
else
    echo "❌ Build test failed"
    exit 1
fi

echo ""
echo "🎉 Frontend installation completed successfully!"
echo ""
echo "📋 Available commands:"
echo "   Development: ./start-development.sh (or npm run dev)"
echo "   Production:  ./start-production.sh (or serve -s dist)"
echo "   Build:       npm run build"
echo "   Deploy:      ./deploy.sh"
echo ""
echo "🌐 URLs:"
echo "   Development: http://localhost:5173"
echo "   Production:  http://localhost:3000 (with serve)"
echo ""
echo "📁 Build output: ./dist/"
echo "⚙️  Configuration: .env.local"
echo ""
echo "🔧 Web server templates:"
echo "   Nginx: nginx.conf.template"
echo "   Apache: apache.conf.template"
echo ""
echo "📚 Default admin login:"
echo "   Email: <EMAIL>"
echo "   Password: admin123"
echo ""
