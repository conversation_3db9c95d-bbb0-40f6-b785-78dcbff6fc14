const Transaction = require('../models/Transaction');
const Store = require('../models/Store');

/**
 * Get comprehensive financial data for a store
 */
exports.getStoreFinancials = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { timeframe = 'month' } = req.query;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Calculate date range based on timeframe
    const now = new Date();
    let startDate, previousStartDate;
    
    switch (timeframe) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        previousStartDate = new Date(startDate.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        previousStartDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        break;
      case 'quarter':
        const quarter = Math.floor(now.getMonth() / 3);
        startDate = new Date(now.getFullYear(), quarter * 3, 1);
        previousStartDate = new Date(now.getFullYear(), (quarter - 1) * 3, 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        previousStartDate = new Date(now.getFullYear() - 1, 0, 1);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        previousStartDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    }

    // Get current period financial data
    const currentPeriodData = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$amount' },
          totalTransactions: { $sum: 1 },
          payfastFees: { 
            $sum: { 
              $multiply: ['$amount', 0.025] // 2.5% PayFast fee
            }
          }
        }
      }
    ]);

    // Get previous period data for comparison
    const previousPeriodData = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: previousStartDate, $lt: startDate }
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$amount' },
          totalTransactions: { $sum: 1 },
          payfastFees: { 
            $sum: { 
              $multiply: ['$amount', 0.025]
            }
          }
        }
      }
    ]);

    const current = currentPeriodData[0] || { totalRevenue: 0, totalTransactions: 0, payfastFees: 0 };
    const previous = previousPeriodData[0] || { totalRevenue: 0, totalTransactions: 0, payfastFees: 0 };

    // Calculate profit (simplified - revenue minus estimated costs)
    const estimatedCosts = current.totalRevenue * 0.6; // Assume 60% cost of goods
    const netProfit = current.totalRevenue - estimatedCosts - current.payfastFees;
    const previousNetProfit = previous.totalRevenue - (previous.totalRevenue * 0.6) - previous.payfastFees;

    // Calculate VAT (15% of revenue)
    const vatRate = 0.15;
    const taxCollected = current.totalRevenue * vatRate;
    const previousTaxCollected = previous.totalRevenue * vatRate;

    // Calculate percentage changes
    const revenueChange = previous.totalRevenue > 0 
      ? ((current.totalRevenue - previous.totalRevenue) / previous.totalRevenue * 100).toFixed(1)
      : 0;
    
    const profitChange = previousNetProfit > 0
      ? ((netProfit - previousNetProfit) / previousNetProfit * 100).toFixed(1)
      : 0;
    
    const feesChange = previous.payfastFees > 0
      ? ((current.payfastFees - previous.payfastFees) / previous.payfastFees * 100).toFixed(1)
      : 0;
    
    const taxChange = previousTaxCollected > 0
      ? ((taxCollected - previousTaxCollected) / previousTaxCollected * 100).toFixed(1)
      : 0;

    // Get revenue trend data
    const revenueData = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          revenue: { $sum: '$amount' },
          profit: { 
            $sum: { 
              $subtract: [
                '$amount',
                { $add: [
                  { $multiply: ['$amount', 0.6] }, // Cost of goods
                  { $multiply: ['$amount', 0.025] } // PayFast fees
                ]}
              ]
            }
          }
        }
      },
      {
        $project: {
          date: {
            $dateToString: {
              format: "%Y-%m-%d",
              date: {
                $dateFromParts: {
                  year: '$_id.year',
                  month: '$_id.month',
                  day: '$_id.day'
                }
              }
            }
          },
          revenue: 1,
          profit: 1
        }
      },
      { $sort: { date: 1 } }
    ]);

    // Get PayFast fees breakdown
    const payfastFeesBreakdown = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$payfast.paymentMethod',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          fees: { $sum: { $multiply: ['$amount', 0.025] } }
        }
      },
      {
        $project: {
          type: { $ifNull: ['$_id', 'Card Payment'] },
          count: 1,
          totalAmount: 1,
          fees: 1,
          netAmount: { $subtract: ['$totalAmount', '$fees'] }
        }
      }
    ]);

    // Calculate profit analysis
    const grossMargin = current.totalRevenue > 0 
      ? (((current.totalRevenue - estimatedCosts) / current.totalRevenue) * 100).toFixed(1)
      : 0;
    
    const netMargin = current.totalRevenue > 0 
      ? ((netProfit / current.totalRevenue) * 100).toFixed(1)
      : 0;
    
    const avgTransactionProfit = current.totalTransactions > 0 
      ? (netProfit / current.totalTransactions).toFixed(2)
      : 0;

    res.status(200).json({
      totalRevenue: current.totalRevenue,
      netProfit,
      payfastFees: current.payfastFees,
      taxCollected,
      revenueChange: parseFloat(revenueChange),
      profitChange: parseFloat(profitChange),
      feesChange: parseFloat(feesChange),
      taxChange: parseFloat(taxChange),
      revenueData: revenueData.map(item => ({
        date: item.date,
        revenue: item.revenue,
        profit: item.profit
      })),
      payfastFees: payfastFeesBreakdown,
      taxData: {
        vatCollected: taxCollected,
        vatRate: vatRate * 100,
        compliance: {
          allTransactionsRecorded: true,
          vatCalculationsUpToDate: true,
          monthlyReturnDue: 5 // days
        }
      },
      profitData: {
        grossMargin: parseFloat(grossMargin),
        netMargin: parseFloat(netMargin),
        avgTransactionProfit: parseFloat(avgTransactionProfit),
        profitGrowth: parseFloat(profitChange)
      },
      timeframe,
      dateRange: {
        start: startDate,
        end: now
      }
    });

  } catch (error) {
    console.error('Financial data error:', error);
    res.status(500).json({ 
      message: 'Failed to get financial data', 
      error: error.message 
    });
  }
};

/**
 * Generate tax report
 */
exports.generateTaxReport = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { startDate, endDate, format = 'json' } = req.query;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    // Get tax data
    const taxData = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          totalSales: { $sum: '$amount' },
          vatCollected: { $sum: { $multiply: ['$amount', 0.15] } },
          transactionCount: { $sum: 1 }
        }
      },
      {
        $project: {
          period: {
            $concat: [
              { $toString: '$_id.year' },
              '-',
              { $toString: '$_id.month' }
            ]
          },
          totalSales: 1,
          vatCollected: 1,
          transactionCount: 1,
          netSales: { $subtract: ['$totalSales', '$vatCollected'] }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } }
    ]);

    if (format === 'csv') {
      // Generate CSV format
      const csvHeader = 'Period,Total Sales,VAT Collected,Net Sales,Transaction Count\n';
      const csvData = taxData.map(row => 
        `${row.period},${row.totalSales},${row.vatCollected},${row.netSales},${row.transactionCount}`
      ).join('\n');
      
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=tax_report.csv');
      res.send(csvHeader + csvData);
    } else {
      res.status(200).json({
        taxReport: taxData,
        summary: {
          totalSales: taxData.reduce((sum, item) => sum + item.totalSales, 0),
          totalVAT: taxData.reduce((sum, item) => sum + item.vatCollected, 0),
          totalTransactions: taxData.reduce((sum, item) => sum + item.transactionCount, 0)
        },
        period: { start, end }
      });
    }

  } catch (error) {
    console.error('Tax report error:', error);
    res.status(500).json({ 
      message: 'Failed to generate tax report', 
      error: error.message 
    });
  }
};

/**
 * Get profit analysis
 */
exports.getProfitAnalysis = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { timeframe = 'month' } = req.query;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Get detailed profit analysis
    const profitAnalysis = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed'
        }
      },
      { $unwind: '$items' },
      {
        $group: {
          _id: '$items.name',
          totalRevenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } },
          totalQuantity: { $sum: '$items.quantity' },
          avgPrice: { $avg: '$items.price' }
        }
      },
      {
        $project: {
          productName: '$_id',
          totalRevenue: 1,
          totalQuantity: 1,
          avgPrice: 1,
          estimatedCost: { $multiply: ['$totalRevenue', 0.6] },
          estimatedProfit: { $multiply: ['$totalRevenue', 0.4] },
          profitMargin: 40 // Estimated 40% profit margin
        }
      },
      { $sort: { totalRevenue: -1 } },
      { $limit: 10 }
    ]);

    res.status(200).json({
      profitAnalysis,
      timeframe
    });

  } catch (error) {
    console.error('Profit analysis error:', error);
    res.status(500).json({ 
      message: 'Failed to get profit analysis', 
      error: error.message 
    });
  }
};
