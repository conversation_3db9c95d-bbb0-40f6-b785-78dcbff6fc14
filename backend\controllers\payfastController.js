const axios = require('axios');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const Transaction = require('../models/Transaction');
const Store = require('../models/Store');

// PayFast configuration
const PAYFAST_CONFIG = {
  sandbox: {
    baseUrl: 'https://sandbox.payfast.co.za',
    validateUrl: 'https://sandbox.payfast.co.za/eng/query/validate'
  },
  production: {
    baseUrl: 'https://www.payfast.co.za',
    validateUrl: 'https://www.payfast.co.za/eng/query/validate'
  }
};

/**
 * Generate PayFast signature for request validation
 */
function generateSignature(data, passphrase) {
  // Create parameter string
  const paramString = Object.keys(data)
    .sort()
    .filter(key => key !== 'signature')
    .map(key => `${key}=${encodeURIComponent(data[key])}`)
    .join('&');
  
  // Add passphrase if provided
  const stringToHash = passphrase ? 
    `${paramString}&passphrase=${encodeURIComponent(passphrase)}` : 
    paramString;
  
  // Generate MD5 hash
  return crypto.createHash('md5').update(stringToHash).digest('hex');
}

/**
 * Verify PayFast signature from webhook
 */
function verifySignature(data, signature, passphrase) {
  const calculatedSignature = generateSignature(data, passphrase);
  return calculatedSignature === signature;
}

/**
 * Initiate payment with PayFast
 */
exports.initiatePayment = async (req, res) => {
  try {
    const { storeId, systemId, amount, items, paymentMethod = 'card' } = req.body;

    // Validate required fields
    if (!storeId || !amount || !items || items.length === 0) {
      return res.status(400).json({ 
        message: 'Missing required fields: storeId, amount, items' 
      });
    }

    // Get store and PayFast configuration
    const store = await Store.findById(storeId);
    if (!store) {
      return res.status(404).json({ message: 'Store not found' });
    }

    if (!store.payfast || !store.payfast.setupComplete) {
      return res.status(400).json({ 
        message: 'PayFast not configured for this store' 
      });
    }

    // Generate unique transaction ID
    const transactionId = uuidv4();
    
    // Create transaction record
    const transaction = new Transaction({
      storeId,
      systemId,
      amount,
      currency: 'ZAR',
      status: 'pending',
      transactionId,
      items: items.map(item => ({
        productId: item.productId,
        name: item.name,
        price: item.price,
        quantity: item.quantity || 1,
        barcode: item.barcode
      }))
    });

    await transaction.save();

    // Prepare PayFast payment data
    const payfastConfig = PAYFAST_CONFIG[store.payfast.sandbox ? 'sandbox' : 'production'];
    const baseUrl = process.env.BASE_URL || 'http://localhost:5000';
    
    const paymentData = {
      merchant_id: store.payfast.merchantId,
      merchant_key: store.payfast.merchantKey,
      return_url: `${baseUrl}/api/payments/return`,
      cancel_url: `${baseUrl}/api/payments/cancel`,
      notify_url: `${baseUrl}/api/payments/webhook`,
      name_first: 'Self',
      name_last: 'Checkout',
      email_address: '<EMAIL>',
      m_payment_id: transactionId,
      amount: amount.toFixed(2),
      item_name: `Self-checkout purchase - ${items.length} items`,
      item_description: `Store: ${store.name}`,
      custom_str1: transactionId,
      custom_str2: storeId,
      custom_str3: systemId
    };

    // Generate signature
    paymentData.signature = generateSignature(paymentData, store.payfast.passphrase);

    // For POS integration, we would typically make an API call to PayFast
    // For now, we'll return the payment data for the frontend to handle
    res.status(200).json({
      success: true,
      message: 'Payment initiated successfully',
      transactionId,
      paymentData,
      payfastUrl: `${payfastConfig.baseUrl}/eng/process`,
      amount: amount.toFixed(2),
      currency: 'ZAR'
    });

  } catch (error) {
    console.error('Payment initiation error:', error);
    res.status(500).json({ 
      message: 'Failed to initiate payment', 
      error: error.message 
    });
  }
};

/**
 * Handle PayFast webhook notifications
 */
exports.handleWebhook = async (req, res) => {
  try {
    const data = req.body;
    
    // Extract transaction details
    const transactionId = data.custom_str1;
    const storeId = data.custom_str2;
    
    if (!transactionId || !storeId) {
      return res.status(400).json({ message: 'Invalid webhook data' });
    }

    // Get store for signature verification
    const store = await Store.findById(storeId);
    if (!store) {
      return res.status(404).json({ message: 'Store not found' });
    }

    // Verify signature
    const signature = data.signature;
    delete data.signature; // Remove signature from data for verification
    
    if (!verifySignature(data, signature, store.payfast.passphrase)) {
      return res.status(400).json({ message: 'Invalid signature' });
    }

    // Find transaction
    const transaction = await Transaction.findOne({ transactionId });
    if (!transaction) {
      return res.status(404).json({ message: 'Transaction not found' });
    }

    // Update transaction based on payment status
    const paymentStatus = data.payment_status;
    let transactionStatus = 'pending';
    
    switch (paymentStatus) {
      case 'COMPLETE':
        transactionStatus = 'completed';
        transaction.completedAt = new Date();
        break;
      case 'FAILED':
        transactionStatus = 'failed';
        break;
      case 'CANCELLED':
        transactionStatus = 'cancelled';
        break;
      default:
        transactionStatus = 'pending';
    }

    // Update transaction with PayFast data
    transaction.status = transactionStatus;
    transaction.payfast = {
      paymentId: data.pf_payment_id,
      merchantTransactionId: data.m_payment_id,
      signature: signature,
      paymentMethod: data.payment_method || 'card',
      cardType: data.card_type,
      maskedCardNumber: data.masked_card_number
    };

    await transaction.save();

    // Send success response to PayFast
    res.status(200).send('OK');

  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).json({ 
      message: 'Webhook processing failed', 
      error: error.message 
    });
  }
};

/**
 * Check payment status
 */
exports.checkPaymentStatus = async (req, res) => {
  try {
    const { transactionId } = req.params;
    
    const transaction = await Transaction.findOne({ transactionId })
      .populate('storeId', 'name')
      .populate('items.productId', 'name barcode');
    
    if (!transaction) {
      return res.status(404).json({ message: 'Transaction not found' });
    }

    res.status(200).json({
      transactionId: transaction.transactionId,
      status: transaction.status,
      amount: transaction.amount,
      currency: transaction.currency,
      items: transaction.items,
      store: transaction.storeId,
      payfast: transaction.payfast,
      createdAt: transaction.createdAt,
      completedAt: transaction.completedAt
    });

  } catch (error) {
    console.error('Status check error:', error);
    res.status(500).json({ 
      message: 'Failed to check payment status', 
      error: error.message 
    });
  }
};

/**
 * Process refund
 */
exports.processRefund = async (req, res) => {
  try {
    const { transactionId, amount, reason } = req.body;
    
    const transaction = await Transaction.findOne({ transactionId })
      .populate('storeId');
    
    if (!transaction) {
      return res.status(404).json({ message: 'Transaction not found' });
    }

    if (transaction.status !== 'completed') {
      return res.status(400).json({ 
        message: 'Can only refund completed transactions' 
      });
    }

    const store = transaction.storeId;
    const refundAmount = amount || transaction.amount;

    // Prepare refund data for PayFast
    const refundData = {
      merchant_id: store.payfast.merchantId,
      version: 'v1',
      timestamp: new Date().toISOString(),
      signature: '' // Will be generated
    };

    // For now, mark transaction as refunded
    // In production, you would make API call to PayFast for actual refund
    transaction.status = 'refunded';
    await transaction.save();

    res.status(200).json({
      success: true,
      message: 'Refund processed successfully',
      transactionId,
      refundAmount,
      originalAmount: transaction.amount
    });

  } catch (error) {
    console.error('Refund processing error:', error);
    res.status(500).json({ 
      message: 'Failed to process refund', 
      error: error.message 
    });
  }
};

/**
 * Get payment history for a store
 */
exports.getPaymentHistory = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { page = 1, limit = 50, status, startDate, endDate } = req.query;

    // Build query
    const query = { storeId };
    
    if (status) {
      query.status = status;
    }
    
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    // Execute query with pagination
    const transactions = await Transaction.find(query)
      .populate('items.productId', 'name barcode')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Transaction.countDocuments(query);

    res.status(200).json({
      transactions,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Payment history error:', error);
    res.status(500).json({ 
      message: 'Failed to get payment history', 
      error: error.message 
    });
  }
};
